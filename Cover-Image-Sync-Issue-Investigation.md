# Book Cover Image Sync Issue Investigation Report

## Issue Description
Book cover images are not being re-imported on different devices through the sync system. When syncing a backup to a new device with an empty database, all content (notes, folders, books) is properly added except for the book cover images. The cover images exist in the backup folder but aren't being added to the `media_files` table with the correct `book_id` relationship.

## Investigation Summary

### 1. How Cover Images Are Initially Added to Database

When a book is created through the application:

1. **Book Creation Process** (`books-api.ts:1045-1095`):
   - If a cover URL is provided, it's downloaded using `downloadCoverImageData()`
   - If the URL is a data URL (base64), it's converted to a Buffer
   - The cover is saved using `saveBookCover()` which:
     - Deletes any existing cover for the book
     - Creates a new entry in the `media_files` table
     - Stores the physical file in the user's media directory
   - **OPTIMIZATION**: After saving, the base64 data URL is cleared from the book's `cover_url` field to prevent sync manifest bloat

2. **Media Storage** (`media-api.ts:168-181`):
   - `saveBookCover()` creates a media file entry with:
     - `book_id`: The book's ID
     - `is_cover`: Set to `true`
     - `file_path`: Absolute path to the stored image file
     - Physical file saved with timestamp prefix to prevent collisions

### 2. How Cover Images Are Exported During Backup

During the sync/export process:

1. **Export Logic** (`unified-sync-engine.ts:1103-1165`):
   - The sync engine queries the `media_files` table directly for book covers (line 1136-1137)
   - **CRITICAL**: It doesn't rely on `cover_url` field (which is null after optimization)
   - If a cover exists in `media_files`:
     - The cover file is copied to the book's backup folder as `.cover.jpg` (hidden file)
     - The metadata includes `coverImage: '.cover.jpg'` to indicate a cover exists

2. **Manifest Generation** (`manifest-manager.ts:299-304`):
   - The manifest explicitly excludes base64 cover URLs to prevent bloat
   - Cover images are handled separately as physical files, not stored in the manifest

### 3. How Cover Images Should Be Imported from Backup

During the import process:

1. **Import Logic** (`unified-sync-engine.ts:838-861`):
   - The import checks for `metadata.coverImage` in the manifest item
   - If found, it:
     - Constructs the path to the `.cover.jpg` file
     - Reads the file as a Buffer
     - Saves it using `saveMediaFile()` to create the media_files entry
     - Updates the book's `cover_url` with the media URL

2. **Import Handler** (`import-handler.ts:161-165`):
   - Also recognizes `.cover.jpg` files and stores the path for processing

## Root Cause Analysis

After thorough investigation and examining a real manifest file, the root cause has been identified:

### **Incorrect Parameter Passing in `updateManifestWithExport()`**

The issue is in how the book metadata is passed to the manifest update function:

1. **In `unified-sync-engine.ts:exportBook()` (line ~1157)**:
   ```typescript
   // Current INCORRECT code:
   manifestManager.updateManifestWithExport(manifest, {
     ...book,
     type: 'book',
     name: book.title,
     metadata: bookMeta  // ❌ Wrong - this is inside the item object
   }, path.relative(directory, bookPath));
   ```

2. **The Problem**:
   - The `updateManifestWithExport()` function signature expects 4 parameters:
     1. `manifest: SyncManifest`
     2. `item: any` 
     3. `relativePath: string`
     4. `metadata?: any` (optional 4th parameter)
   - The `bookMeta` object (containing `coverImage`) is being passed as a property inside the item object instead of as the 4th parameter
   - When `updateManifestWithExport()` calls `extractMetadata()`, it extracts metadata from the item object, not from the passed metadata
   - The `coverImage` field that was added to `bookMeta` is ignored

3. **Evidence from Real Manifest**:
   - The `.cover.jpg` file IS exported to the sync directory
   - But the manifest metadata for the book does NOT include `coverImage`
   - During import, the code checks for `metadata.coverImage` and skips cover import when it's missing

### The Fix Needed

The fix is simple - pass `bookMeta` as the 4th parameter:

```typescript
// In unified-sync-engine.ts, exportBook method, line ~1157:
manifestManager.updateManifestWithExport(manifest, {
  ...book,
  type: 'book',
  name: book.title
}, path.relative(directory, bookPath), bookMeta);  // ✅ Pass bookMeta as 4th parameter
```

This ensures that the `coverImage` field from `bookMeta` is properly included in the manifest metadata.

## Additional Findings

1. **Database Optimization**: The system clears base64 cover URLs from the database after saving to media_files to prevent sync manifest bloat. This is a good optimization but requires careful handling during sync.

2. **Media File Handling**: The system properly handles media files with:
   - Unique filenames using timestamps
   - Proper file path validation
   - Atomic file operations

3. **Import Fallback**: The import handler has logic to detect `.cover.jpg` files but may not be properly integrated with the main import flow.

## Recommendations

1. **Immediate Fix**: Ensure `coverImage` metadata is included in the manifest during export
2. **Testing**: Add specific tests for cover image sync scenarios
3. **Documentation**: Update sync documentation to clarify cover image handling
4. **Consider**: Adding a verification step after import to check if all expected media files were imported

## Impact

This issue affects all users trying to sync their book collections across devices. Books will sync without their cover images, requiring users to manually re-add covers or triggering a re-download from online sources (if available).

## Files Involved

- `/electron/main/api/books-api.ts` - Book creation and cover download
- `/electron/main/api/media-api.ts` - Media file storage
- `/electron/main/api/sync-logic/unified-sync-engine.ts` - Export/import logic
- `/electron/main/api/sync-logic/manifest-manager.ts` - Manifest generation
- `/electron/main/api/sync-logic/import-handler.ts` - Import handling