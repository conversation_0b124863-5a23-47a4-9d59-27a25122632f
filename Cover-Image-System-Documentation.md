# Cover Image System - Complete Documentation

## Overview
This document provides a comprehensive breakdown of all files and functions responsible for saving book cover images to the backup/sync directory and syncing them across different devices in the Noti application.

## Core System Architecture

The cover image system operates through several interconnected components:

1. **Local Storage**: Cover images are stored in the `media_files` table with `is_cover = 1`
2. **Sync Export**: Covers are exported as hidden `.cover.jpg` files in book folders
3. **Sync Import**: Covers are imported from `.cover.jpg` files and recreated in local database
4. **File Operations**: Atomic read/write operations for cover image files

---

## 1. COVER IMAGE SAVING TO BACKUP/SYNC DIRECTORY

### Primary File: `electron/main/api/sync-logic/unified-sync-engine.ts`

#### Cover Export Logic (Lines 1133-1156)
```typescript
// Handle cover export - save as hidden .cover.jpg file in book folder
if (book.cover_url) {
  // Extract media file info from cover_url
  const mediaQuery = `SELECT * FROM media_files WHERE book_id = ? AND is_cover = 1`;
  const mediaFile = await dbGet<any>(mediaQuery, [book.id]);
  
  if (mediaFile) {
    const coverFileName = '.cover.jpg';
    const coverPath = path.join(bookPath, coverFileName);
    
    try {
      // Copy cover file - use readFileBuffer for media files
      const sourceData = await fileOperations.readFileBuffer(mediaFile.file_path);
      await fileOperations.writeFileBuffer(coverPath, sourceData);
      
      // Mark that cover exists in metadata
      (bookMeta as any).coverImage = coverFileName;
    } catch (error) {
      console.error(`Failed to export cover for book ${book.id}:`, error);
      // Continue with export even if cover fails
    }
  }
}
```

**What this does:**
- Queries the `media_files` table for cover images linked to the book
- Reads the cover image file from local storage using `readFileBuffer`
- Writes the cover as a hidden `.cover.jpg` file in the book's sync folder
- Updates the book metadata to indicate a cover exists

---

## 2. COVER IMAGE SYNCING ON DIFFERENT DEVICES

### Primary File: `electron/main/api/sync-logic/unified-sync-engine.ts`

#### Cover Import Logic (Lines 837-861)
```typescript
// Handle cover image if present (now stored as hidden file)
if (metadata.coverImage) {
  const coverPath = path.join(bookPath, metadata.coverImage);
  try {
    if (await fileOperations.exists(coverPath)) {
      const coverBuffer = await fileOperations.readFileBuffer(coverPath);
      const mediaFile = await saveMediaFile(
        null,
        coverBuffer,
        metadata.coverImage,
        'image/jpeg',
        book.id,
        true
      );
      
      // Update book with cover URL
      await updateBook(book.id!, {
        cover_url: filePathToMediaUrl(mediaFile.file_path)
      });
    }
  } catch (error) {
    console.error(`Failed to import cover for book ${book.id}:`, error);
    // Continue with import even if cover fails
  }
}
```

**What this does:**
- Checks if the book metadata indicates a cover image exists
- Reads the `.cover.jpg` file from the sync directory
- Saves the cover to the local `media_files` table using `saveMediaFile`
- Updates the book record with the new cover URL

---

## 3. MEDIA FILE OPERATIONS FOR COVER IMAGES

### Primary File: `electron/main/api/media-api.ts`

#### Save Book Cover Function (Lines 167-181)
```typescript
export const saveBookCover = async (
  bookId: number,
  coverData: Buffer,
  fileName: string = 'cover.jpg'
): Promise<MediaFile> => {
  // First, delete any existing cover for this book
  const existingCover = await getBookCover(bookId);
  if (existingCover && existingCover.id) {
    await deleteMediaFile(existingCover.id);
  }

  // Save the new cover
  return await saveMediaFile(null, coverData, fileName, 'image/jpeg', bookId, true);
};
```

#### Get Book Cover Function (Lines 154-165)
```typescript
export const getBookCover = async (bookId: number): Promise<MediaFile | null> => {
  const query = 'SELECT * FROM media_files WHERE book_id = ? AND is_cover = 1 ORDER BY created_at DESC LIMIT 1';

  try {
    const result = await dbAll<MediaFile>(query, [bookId]);
    return result.length > 0 ? result[0] : null;
  } catch (error) {
    console.error(`Error getting book cover for book ID ${bookId}:`, error);
    throw error;
  }
};
```

#### Core Save Media File Function (Lines 69-116)
```typescript
export const saveMediaFile = async (
  noteId: number | null,
  fileData: number[] | Buffer,
  fileName: string,
  fileType: string,
  bookId: number | null = null,
  isCover: boolean = false
): Promise<MediaFile> => {
  try {
    // Create unique filename to prevent collisions
    const timestamp = Date.now();
    const uniqueFileName = `${timestamp}-${fileName}`;
    const mediaStoragePath = getMediaStoragePath();
    const filePath = path.join(mediaStoragePath, uniqueFileName);

    // Convert array to Buffer if needed
    const fileBuffer = Buffer.isBuffer(fileData) ? fileData : Buffer.from(fileData);

    // Save file to disk
    fs.writeFileSync(filePath, fileBuffer);

    // Get file size
    const fileSize = fileBuffer.length;

    // Save entry to database
    const query = `
      INSERT INTO media_files (
        note_id, book_id, file_path, file_name, file_type, file_size, is_cover, created_at
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
    `;

    const result: RunResult = await dbRun(query, [
      noteId, bookId, filePath, uniqueFileName, fileType, fileSize, isCover ? 1 : 0
    ]);

    if (!result.lastID) {
      throw new Error('Failed to create media file entry, no ID returned');
    }

    // Return the created media file
    return getMediaFileById(result.lastID);
  } catch (error) {
    console.error('Error saving media file:', error);
    throw error;
  }
};
```

**What these functions do:**
- `saveBookCover`: Replaces any existing cover for a book with a new one
- `getBookCover`: Retrieves the current cover for a book from the database
- `saveMediaFile`: Core function that saves any media file (including covers) to disk and database

---

## 4. FILE OPERATIONS FOR COVER IMAGE HANDLING

### Primary File: `electron/main/api/sync-logic/file-operations.ts`

#### Read File Buffer (Lines 299-313)
```typescript
async readFileBuffer(filePath: string): Promise<Buffer> {
  try {
    const content = await fs.readFile(filePath);
    return content;
  } catch (error) {
    throw new SyncError(
      ErrorCode.FILE_READ_ERROR,
      `Failed to read file: ${(error as Error).message}`
    );
  }
}
```

#### Write File Buffer (Lines 315-350)
```typescript
async writeFileBuffer(filePath: string, content: Buffer): Promise<void> {
  // Validate path if sync directory is set
  const validatedPath = this.syncDirectory
    ? this.validatePath(filePath, this.syncDirectory)
    : path.normalize(filePath);

  const dir = path.dirname(validatedPath);
  const basename = path.basename(validatedPath);
  const tempPath = path.join(dir, `.${basename}.tmp`);

  try {
    // Ensure directory exists
    await this.ensurePath(dir);

    // Write to temp file
    await fs.writeFile(tempPath, content);

    // Rename to final path (atomic on same filesystem)
    await fs.rename(tempPath, validatedPath);
  } catch (error) {
    // Clean up temp file if it exists
    try {
      await fs.unlink(tempPath);
    } catch (cleanupError) {
      // Ignore cleanup errors
    }

    throw new SyncError(
      ErrorCode.FILE_WRITE_ERROR,
      `Failed to write file: ${(error as Error).message}`
    );
  }
}
```

**What these functions do:**
- `readFileBuffer`: Reads cover image files as binary data for sync operations
- `writeFileBuffer`: Writes cover image files atomically to prevent corruption

---

## 5. IMPORT HANDLER FOR COVER IMAGES

### Primary File: `electron/main/api/sync-logic/import-handler.ts`

#### Handle Hidden Cover Files (Lines 160-165)
```typescript
// Handle hidden cover files
if (entry.name === '.cover.jpg' && entry.isFile()) {
  // Store cover file path for later processing
  book.coverPath = entryPath;
  continue;
}
```

**What this does:**
- Detects `.cover.jpg` files during directory parsing
- Stores the cover file path for later processing during import
- Skips processing the cover as a regular file

---

## 6. BOOK CREATION WITH COVER PROCESSING

### Primary File: `electron/main/api/books-api.ts`

#### Immediate Cover Processing After Book Creation (Lines 1078-1094)
```typescript
// CRITICAL FIX: Process cover immediately after book creation to prevent timing issues
if (createdBook.id && createdBook.cover_url && isDataUrl(createdBook.cover_url)) {
  try {
    console.log(`Processing cover immediately for book "${createdBook.title}" (ID: ${createdBook.id})`);
    const coverBuffer = dataUrlToBuffer(createdBook.cover_url);
    await saveBookCover(createdBook.id, coverBuffer, 'cover.jpg');
    console.log(`✓ Cover processed and saved immediately for book "${createdBook.title}"`);

    // OPTIMIZATION: Clear base64 data URL from database to prevent sync manifest bloat
    // The cover is now stored in media_files table, so we don't need the base64 data in cover_url
    await updateBook(createdBook.id, { cover_url: null });
    console.log(`✓ Cleared base64 data URL from database for book "${createdBook.title}" to optimize sync`);
  } catch (coverError) {
    console.error(`Failed to process cover immediately for book "${createdBook.title}":`, coverError);
    // Don't fail book creation if cover processing fails
  }
}
```

#### Cover Download During Book Creation (Lines 1047-1060)
```typescript
// Download cover image if requested and URL is provided
if (downloadCover && validatedBookData.cover_url && !isDataUrl(validatedBookData.cover_url)) {
  try {
    console.log(`Attempting to download cover from: ${validatedBookData.cover_url}`);
    const imageData = await downloadCoverImageData(validatedBookData.cover_url);
    const base64Image = `data:image/jpeg;base64,${imageData.toString('base64')}`;
    validatedBookData.cover_url = base64Image;
    console.log('Cover image downloaded and converted to base64.');
  } catch (error) {
    console.error('Failed to download or process cover image, proceeding without it:', error);
    // Optionally, set cover_url to null or a placeholder if download fails
    validatedBookData.cover_url = null;
  }
}
```

#### Batch Cover Download (Lines 1006-1028)
```typescript
const downloadPromises = batch.map(async (book) => {
  try {
    console.log(`Downloading cover for "${book.title}"...`);

    // Check if it's a data URL (from manual upload)
    if (isDataUrl(book.cover_url)) {
      const coverBuffer = dataUrlToBuffer(book.cover_url);
      await saveBookCover(book.id, coverBuffer, 'cover.jpg');
      console.log(`✓ Saved cover for "${book.title}" from data URL`);
    } else {
      // Download from URL
      const coverData = await downloadCoverImageData(book.cover_url);
      await saveBookCover(book.id, coverData);
      console.log(`✓ Downloaded and saved cover for "${book.title}"`);
    }

    successCount++;
  } catch (error) {
    console.error(`✗ Failed to download cover for "${book.title}":`, error);
    failureCount++;
  }
});
```

**What these functions do:**
- Process cover images immediately after book creation to prevent timing issues
- Download cover images from URLs and convert to base64 for temporary storage
- Handle batch downloading of covers for multiple books
- Clear base64 data from database after saving to media_files to optimize sync

---

## 7. IPC HANDLERS FOR COVER OPERATIONS

### Primary File: `electron/main/ipc-handlers.ts`

#### Media Handlers (Lines 647-747)
```typescript
// Save book cover specifically
ipcMain.handle('media:saveBookCover', async (_event: IpcMainInvokeEvent,
    bookId: number, coverData: Uint8Array | number[], fileName?: string) => {
    try {
        const MAX_COVER_BYTES = 10 * 1024 * 1024; // 10MB limit
        const dataLength = coverData.length;

        if (dataLength > MAX_COVER_BYTES) {
            throw new Error(`Cover data size (${dataLength} bytes) exceeds maximum allowed size (${MAX_COVER_BYTES} bytes)`);
        }

        // Convert to Buffer efficiently
        const buffer = coverData instanceof Uint8Array ? Buffer.from(coverData) : Buffer.from(coverData);

        return await mediaApi.saveBookCover(bookId, buffer, fileName);
    } catch (error) {
        console.error(`IPC media:saveBookCover error for book ID ${bookId}:`, error);
        throw error;
    }
});

// Get media storage path
ipcMain.handle('media:getStoragePath', async () => {
    try {
        return mediaApi.getMediaStoragePath();
    } catch (error) {
        console.error('IPC media:getStoragePath error:', error);
        throw error;
    }
});
```

#### Books Handlers (Lines 890-940)
```typescript
// Add book from OpenLibrary search result
ipcMain.handle('books:addFromOpenLibrary', async (_event: IpcMainInvokeEvent, searchResult: BookSearchResult) => {
    try {
        return await booksApi.addBookFromOpenLibrary(searchResult);
    } catch (error) {
        console.error('IPC books:addFromOpenLibrary error:', error);
        throw error;
    }
});

// Download cover image
ipcMain.handle('books:downloadCover', async (_event: IpcMainInvokeEvent, coverUrl: string, filename: string) => {
    try {
        return await booksApi.downloadCoverImageData(coverUrl);
    } catch (error) {
        console.error(`IPC books:downloadCover error for URL ${coverUrl}:`, error);
        throw error;
    }
});

// Check and download missing covers
ipcMain.handle('books:checkAndDownloadMissingCovers', async (_event: IpcMainInvokeEvent) => {
    try {
        return await booksApi.checkAndDownloadMissingCovers();
    } catch (error) {
        console.error('IPC books:checkAndDownloadMissingCovers error:', error);
        throw error;
    }
});
```

**What these handlers do:**
- `media:saveBookCover`: Handles cover uploads from frontend with size validation
- `books:addFromOpenLibrary`: Processes book additions with automatic cover download
- `books:downloadCover`: Downloads cover images from URLs
- `books:checkAndDownloadMissingCovers`: Batch processes missing covers

---

## 8. PRELOAD API BRIDGE

### Primary File: `electron/preload/api-bridge.ts`

#### Media API Bridge (Lines 155-183)
```typescript
media: {
  // Save a new media file
  save: (noteId: number | null, fileData: number[], fileName: string, fileType: string) =>
    ipcRenderer.invoke('media:save', noteId, fileData, fileName, fileType),

  // Save book cover specifically
  saveBookCover: (bookId: number, coverData: Uint8Array | number[], fileName?: string) =>
    ipcRenderer.invoke('media:saveBookCover', bookId, coverData, fileName),

  // Get the media storage path
  getStoragePath: () => ipcRenderer.invoke('media:getStoragePath'),

  // Get media URL by file path
  getMediaUrl: (filePath) => ipcRenderer.invoke('media:getMediaUrl', filePath)
},
```

#### Books API Bridge (Lines 129-153)
```typescript
// Add book from OpenLibrary search result
addFromOpenLibrary: (searchResult: BookSearchResult) =>
  ipcRenderer.invoke('books:addFromOpenLibrary', searchResult),

// Download cover image
downloadCover: (coverUrl: string, filename: string) =>
  ipcRenderer.invoke('books:downloadCover', coverUrl, filename),

// Check and download missing covers
checkAndDownloadMissingCovers: () =>
  ipcRenderer.invoke('books:checkAndDownloadMissingCovers'),
```

**What this bridge does:**
- Provides type-safe communication between frontend and backend
- Handles cover upload operations from Vue components
- Manages cover download requests

---

## 9. FRONTEND COMPONENTS FOR COVER HANDLING

### Primary File: `src/components/books/BookCard.vue`

#### Cover Display Logic (Lines 76-98)
```typescript
const coverImageStyle = computed(() => {
  // Priority: cover_media_url > cover_url
  if (props.book.cover_media_url) {
    // Use stored cover image from media_files table
    return {
      backgroundImage: `url(${props.book.cover_media_url})`,
      backgroundSize: 'auto 100%',
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat'
    }
  }

  if (props.book.cover_url) {
    // Fallback to online cover URL
    return {
      backgroundImage: `url(${props.book.cover_url})`,
      backgroundSize: 'auto 100%',
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat'
    }
  }
  return {}
})
```

**What this does:**
- Prioritizes locally stored covers (`cover_media_url`) over online URLs
- Provides fallback to online cover URLs if local cover not available
- Handles responsive cover display with proper sizing

### Primary File: `src/components/modals/BookDetailsModal.vue`

#### Cover Upload and Update (Lines 491-505)
```typescript
try {
  // Convert data URL to buffer and save as book cover
  const response = await fetch(editData.value.cover_url);
  const blob = await response.blob();
  const arrayBuffer = await blob.arrayBuffer();
  const buffer = new Uint8Array(arrayBuffer);

  // Save the cover using the media API (now supports Uint8Array directly)
  await window.db.media.saveBookCover(props.book.id, buffer, 'cover.jpg');
  coverUpdated = true;
  console.log('Cover successfully updated for book:', props.book.id);
} catch (coverError) {
  console.error('Failed to save cover:', coverError);
}
```

**What this does:**
- Handles cover updates in the book details modal
- Converts data URLs to binary data for storage
- Saves updated covers using the media API

### Primary File: `src/components/modals/AddBookManuallyModal.vue`

#### Cover Upload Handling (Lines 417-433)
```typescript
const handleFileChange = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0];

  if (file && file.type.startsWith('image/')) {
    const reader = new FileReader();
    reader.onload = (e) => {
      const newCoverUrl = e.target?.result as string;

      formData.value.cover_url = newCoverUrl;
      userChangedCover.value = true;

      // Emit the cover change to parent so it can track it
      emit('cover-changed', newCoverUrl);
    };
    reader.readAsDataURL(file);
  }
};
```

**What this does:**
- Handles file selection for manual cover uploads
- Converts uploaded images to data URLs for preview
- Emits cover change events to parent components

### Primary File: `src/components/modals/EditBookModal.vue`

#### Similar Cover Upload Logic (Lines 417-433)
```typescript
const handleFileChange = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0];

  if (file && file.type.startsWith('image/')) {
    const reader = new FileReader();
    reader.onload = (e) => {
      const newCoverUrl = e.target?.result as string;

      formData.value.cover_url = newCoverUrl;
      userChangedCover.value = true;

      // Emit the cover change to parent so it can track it
      emit('cover-changed', newCoverUrl);
    };
    reader.readAsDataURL(file);
  }
};
```

**What these components do:**
- `BookCard.vue`: Displays book covers with proper fallback logic
- `BookDetailsModal.vue`: Handles cover updates for existing books
- `AddBookManuallyModal.vue`: Handles cover uploads during manual book creation
- `EditBookModal.vue`: Handles cover changes during book editing

---

## 10. DATABASE SCHEMA FOR COVER STORAGE

### Primary File: `electron/main/database/database.ts`

#### Books Table (Lines 114-132)
```sql
CREATE TABLE IF NOT EXISTS books (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    author TEXT,
    isbn TEXT,
    cover_url TEXT,  -- Only for fallback/reference
    publication_date TEXT,
    description TEXT,
    page_count INTEGER,
    current_page INTEGER,
    rating INTEGER,
    language TEXT,
    genres TEXT,
    olid TEXT,
    status TEXT,
    custom_fields TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### Media Files Table (Lines 178-191)
```sql
CREATE TABLE IF NOT EXISTS media_files (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    note_id INTEGER,
    book_id INTEGER,           -- Support for book covers
    file_path TEXT NOT NULL,
    file_name TEXT NOT NULL,
    file_type TEXT,
    file_size INTEGER,
    is_cover BOOLEAN DEFAULT 0, -- Flag for cover images
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (note_id) REFERENCES notes(id) ON DELETE CASCADE,
    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
);
```

**Database Design:**
- `books.cover_url`: Stores fallback URLs or temporary base64 data
- `media_files.book_id`: Links cover images to specific books
- `media_files.is_cover`: Boolean flag to identify cover images
- Foreign key constraints ensure data integrity

---

## 11. HELPER FUNCTIONS AND UTILITIES

### Primary File: `electron/main/api/books-api.ts`

#### Data URL Utilities (Lines 964-973)
```typescript
// Helper function to check if a string is a data URL (base64 image)
const isDataUrl = (url: string): boolean => {
  return url.startsWith('data:image/');
};

// Helper function to convert data URL to buffer
const dataUrlToBuffer = (dataUrl: string): Buffer => {
  const base64Data = dataUrl.split(',')[1];
  return Buffer.from(base64Data, 'base64');
};
```

#### Cover Download Function (Lines 122-150)
```typescript
// Helper function to download cover image as binary data
const downloadCoverImageData = async (coverUrl: string): Promise<Buffer> => {
  const downloadWithRedirects = (url: string, redirectCount = 0): Promise<Buffer> => {
    const maxRedirects = 5;

    if (redirectCount > maxRedirects) {
      throw new Error('Too many redirects');
    }

    return new Promise((resolve, reject) => {
      const request = https.get(url, { timeout: 15000 }, (response) => {
        // Handle redirects
        if (response.statusCode && response.statusCode >= 300 && response.statusCode < 400 && response.headers.location) {
          return downloadWithRedirects(response.headers.location, redirectCount + 1)
            .then(resolve)
            .catch(reject);
        }

        // Handle successful response
        if (response.statusCode === 200) {
          const chunks: Buffer[] = [];
          response.on('data', (chunk) => chunks.push(chunk));
          response.on('end', () => resolve(Buffer.concat(chunks)));
        } else {
          reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
        }
      });

      request.on('error', reject);
      request.on('timeout', () => {
        request.destroy();
        reject(new Error('Request timeout'));
      });
    });
  };

  return downloadWithRedirects(coverUrl);
};
```

**What these utilities do:**
- `isDataUrl`: Detects base64 encoded image data URLs
- `dataUrlToBuffer`: Converts base64 data URLs to binary buffers
- `downloadCoverImageData`: Downloads cover images from URLs with redirect handling

---

## 12. COMPLETE FILE LIST

### Backend Files (Electron Main Process)
1. **`electron/main/api/sync-logic/unified-sync-engine.ts`** - Core sync logic for cover export/import
2. **`electron/main/api/media-api.ts`** - Media file operations including cover storage
3. **`electron/main/api/sync-logic/file-operations.ts`** - File I/O operations for sync
4. **`electron/main/api/sync-logic/import-handler.ts`** - Import logic for cover detection
5. **`electron/main/api/books-api.ts`** - Book creation and cover processing
6. **`electron/main/ipc-handlers.ts`** - IPC communication handlers
7. **`electron/main/database/database.ts`** - Database schema definitions
8. **`electron/preload/api-bridge.ts`** - Preload API bridge for frontend communication

### Frontend Files (Vue Components)
9. **`src/components/books/BookCard.vue`** - Book display with cover rendering
10. **`src/components/modals/BookDetailsModal.vue`** - Book editing with cover updates
11. **`src/components/modals/AddBookManuallyModal.vue`** - Manual book creation with cover upload
12. **`src/components/modals/EditBookModal.vue`** - Book editing with cover changes
13. **`src/types/electron-api.d.ts`** - TypeScript interface definitions

---

## 13. SYSTEM FLOW SUMMARY

### Cover Image Saving Flow:
1. **User uploads/selects cover** → Frontend components handle file selection
2. **Convert to data URL** → FileReader converts image to base64 for preview
3. **Save to media_files** → Backend saves binary data to disk and database
4. **Export to sync** → Sync engine copies cover to `.cover.jpg` in book folder
5. **Update manifest** → Manifest tracks cover existence in metadata

### Cover Image Syncing Flow:
1. **Detect sync directory** → Import handler scans for `.cover.jpg` files
2. **Read cover file** → File operations read binary data from sync directory
3. **Save to local database** → Media API saves cover to local media_files table
4. **Update book record** → Book record updated with new cover URL
5. **Display in UI** → Frontend components display synced cover

### Key Design Principles:
- **Atomic Operations**: All file operations use temporary files and atomic renames
- **Error Resilience**: Cover failures don't break book creation or sync
- **Performance Optimization**: Base64 data cleared from database after processing
- **Fallback Logic**: Multiple cover sources with priority system
- **Type Safety**: Full TypeScript coverage for all cover operations

---

## 14. TROUBLESHOOTING COMMON ISSUES

### Issue: Covers not syncing between devices
**Check:**
- Verify `.cover.jpg` files exist in book folders in sync directory
- Check manifest contains `coverImage` metadata for books
- Ensure `media_files` table has entries with `is_cover = 1`

### Issue: Covers not displaying in UI
**Check:**
- Verify `cover_media_url` field is populated in book records
- Check media file paths are accessible
- Ensure proper fallback to `cover_url` if media file missing

### Issue: Cover upload failures
**Check:**
- File size limits (10MB max in IPC handlers)
- Image format validation (must start with 'image/')
- Media storage directory permissions and disk space

This documentation covers every aspect of the cover image system from initial upload through cross-device synchronization.
