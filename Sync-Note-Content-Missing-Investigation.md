# Sync Note Content Missing in Editor - Investigation Report

## Issue Description
When syncing notes to a new device through the backup/sync system, the notes appear correctly in the notes list view (showing content preview) but when opened in the editor, the text content doesn't appear. The database shows content in the `content` column but NOT in the `html_content` column, and the editor only displays from `html_content`.

## Investigation Summary

### 1. Database Structure
The notes table has two content fields:
- `content` (TEXT) - Stores plain text/markdown content
- `html_content` (TEXT) - Stores rich HTML content for the editor

### 2. How Notes Are Created and Saved

When creating a new note (`notes-api.ts:176`):
```typescript
const newNote: Note = {
  title: noteTitle,
  content: '',
  html_content: '<p></p>',  // Initial HTML content
  // ... other fields
};
```

When the editor saves content, it saves ONLY the HTML content. The plain text content is typically extracted from the HTML for search/preview purposes.

### 3. How the Editor Works

The NoteEditor component (`NoteEditor.vue`):
- **Line 403**: Initializes with `props.note.html_content || '<p></p>'`
- **Line 437**: When note changes, loads from `html_content`
- **Line 455**: Watches for external changes to `html_content`

**CRITICAL**: The editor ONLY reads from and writes to `html_content`. It never uses the `content` field.

### 4. How Notes Are Exported

In `unified-sync-engine.ts:exportNote()` (line 1203-1251):
- **Line 1240**: Saves only `note.content` to the .md file
- **Line 1233**: Comment states "htmlContent intentionally excluded - will be regenerated from markdown"
- Metadata saved includes id, title, type, color, timestamps (but NOT html_content)

### 5. How Notes Are Imported

In `unified-sync-engine.ts:importNote()` (line 987-1101):
- **Line 1007**: Reads content from .md file into `content` variable
- **Lines 1076 & 1089**: Sets `html_content: null` with comment "Will be regenerated from markdown content by the editor"
- The plain text content is saved to database `content` field
- `html_content` is explicitly set to null

### 6. Why the Notes List Works

The NoteCard component (`NoteCard.vue:49`):
- Receives `content` prop (plain text)
- Strips any HTML tags and shows first 100 characters
- This is why the preview works - it uses `content`, not `html_content`

## Root Cause Analysis

The sync system makes an incorrect assumption that the editor will regenerate HTML content from markdown, but this never happens because:

1. The editor is a rich text editor (TipTap) that works exclusively with HTML
2. The editor never reads from the `content` field - only from `html_content`
3. There's no automatic conversion from markdown to HTML when a note is loaded
4. Setting `html_content` to null during import leaves the editor with nothing to display

## Impact

- Users see their notes in the list but can't view/edit the content
- The data exists in the database but is inaccessible through the UI
- This affects ALL synced notes on new devices
- Manual intervention would be required to convert content to html_content

## Proposed Solutions

### Solution 1: Convert Markdown to HTML During Import (Recommended)

Modify the import process to convert markdown content to HTML:

```typescript
// In unified-sync-engine.ts, importNote method
import MarkdownIt from 'markdown-it'; // Already used in notes-api.ts
const md = new MarkdownIt();

// Around line 1076 & 1089, instead of:
// html_content: null

// Use:
html_content: content ? md.render(content) : '<p></p>'
```

**Pros:**
- Maintains backward compatibility
- Preserves the lightweight export format (markdown only)
- Works with existing backups
- Minimal code change

**Cons:**
- Loses original HTML formatting (but this is already lost in export)
- Need to ensure MarkdownIt is available in sync engine

### Solution 2: Export Both Content and HTML Content

Modify export to save both formats:

```typescript
// In exportNote, save html_content in metadata
const metadata = {
  // ... existing fields
  html_content: note.html_content
};
```

**Pros:**
- Preserves exact formatting
- No conversion needed on import

**Cons:**
- Significantly increases backup size
- Breaks backward compatibility with existing backups
- Goes against the stated goal of lightweight exports

### Solution 3: Make Editor Support Markdown Import

Modify the editor to check for null html_content and convert from content:

```typescript
// In NoteEditor.vue initialization
const initialContent = props.note.html_content || 
  (props.note.content ? md.render(props.note.content) : '<p></p>');
```

**Pros:**
- Works with existing backups
- No changes to sync system

**Cons:**
- Requires markdown parser in frontend
- May cause confusion if content and html_content diverge
- Performance impact on note loading

## Recommendation

**Implement Solution 1** - Convert markdown to HTML during import. This is the cleanest solution that:
- Fixes the immediate issue
- Works with all existing backups
- Maintains the design principle of lightweight exports
- Requires minimal code changes
- Aligns with the existing comment about regenerating HTML from markdown

Additionally, consider:
1. Adding validation to ensure html_content is never null after import
2. Adding a database migration to fix existing imported notes with null html_content
3. Adding tests specifically for this scenario

## Implementation Steps

1. Import MarkdownIt in unified-sync-engine.ts
2. Modify importNote to convert content to html_content
3. Test with various markdown content types
4. Consider adding a utility function for markdown-to-HTML conversion
5. Add logging to track conversion success/failure
6. Create migration script for existing affected notes

## Files Modified
- `/electron/main/api/sync-logic/unified-sync-engine.ts` (import logic)
- Potentially: `/electron/main/api/sync-logic/types.ts` (if adding conversion utilities)