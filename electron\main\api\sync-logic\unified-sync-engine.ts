import { EventEmitter } from 'events';
import * as path from 'path';
import * as fs from 'fs/promises';
import { getDatabase } from '../../database/database';
import { 
  createBook, 
  createFolder, 
  createNote,
  updateBook,
  updateFolder,
  updateNote,
  deleteBook,
  deleteFolder,
  deleteNote,
  getBookById,
  getFolderById,
  getNoteById,
  dbRun,
  dbGet,
  dbAll,
  withTransaction,
  Book,
  Folder,
  Note
} from '../../database/database-api';
import { saveMediaFile, filePathToMediaUrl, getMediaFilesByNoteId } from '../media-api';
import { manifestManager } from './manifest-manager';
import { ChangeDetector } from './change-detector';
import { ConflictResolver } from './conflict-resolver';
import { fileOperations } from './file-operations';
import { databaseHooks } from '../../database/database-hooks';
import {
  embedMediaFiles,
  restoreEmbeddedMedia,
  createMarkdownPreview,
  extractPlainText,
  replaceMediaUrlsWithEmbedded
} from './media-utils';
import { sanitizeBookTitle, sanitizeFolderName, sanitizeNoteTitle } from '../../../utils/filename-sanitizer';
import {
  SyncResult,
  SyncManifest,
  SyncItem,
  ManifestItem,
  ItemType,
  SyncError,
  ErrorCode,
  SyncProgress,
  ConflictResolution,
  LocalItem,
  NotiFileData,
  NotiMetadata,
  NotiContent
} from './types';

export class UnifiedSyncEngine extends EventEmitter {
  private isRunning: boolean = false;
  private changeDetector = new ChangeDetector();
  private conflictResolver = new ConflictResolver();
  // Phase 3: Temporary mapping of sync IDs to local database IDs during import
  private importIdMapping: Map<string, number> = new Map();
  // Track renamed items for cleanup
  private renamedFolders: Array<{ oldPath: string; newPath: string }> = [];
  private renamedBooks: Array<{ oldPath: string; newPath: string }> = [];
  private renamedNotes: Array<{ oldPath: string; newPath: string }> = [];

  /**
   * Main sync method that orchestrates the entire sync process
   */
  async sync(directory: string): Promise<SyncResult> {
    if (this.isRunning) {
      throw new SyncError(ErrorCode.SYNC_IN_PROGRESS, 'Sync is already in progress');
    }

    this.isRunning = true;
    const startTime = Date.now();
    const result: SyncResult = {
      success: false,
      imported: { books: 0, folders: 0, notes: 0 },
      exported: { books: 0, folders: 0, notes: 0 },
      deleted: { books: 0, folders: 0, notes: 0 },
      conflicts: [],
      errors: [],
      timestamp: new Date().toISOString(),
      itemsImported: 0,
      itemsExported: 0
    };

    try {
      // Set the sync directory for path validation
      fileOperations.setSyncDirectory(directory);
      
      // Phase 3: Clear import ID mapping for fresh sync
      this.importIdMapping.clear();
      // Clear renamed items lists for fresh sync
      this.renamedFolders = [];
      this.renamedBooks = [];
      this.renamedNotes = [];
      
      // Emit sync start event
      this.emitProgress({
        phase: 'scanning',
        total: 100,
        processed: 0,
        percentage: 0,
        progress: 0,
        message: 'Starting sync process...'
      });

      // Load manifest - it should already exist if backup was properly initialized
      this.emitProgress({
        phase: 'scanning',
        total: 100,
        processed: 10,
        percentage: 10,
        progress: 10,
        message: 'Loading sync manifest...'
      });

      let manifest: SyncManifest;
      try {
        manifest = await manifestManager.loadManifest(directory);
      } catch (error) {
        // If manifest doesn't exist, backup wasn't properly initialized
        if (error.message.includes('No manifest found')) {
          console.log('[UnifiedSyncEngine] No manifest found, initializing backup location');
          await manifestManager.initializeBackupLocation(directory);
          manifest = await manifestManager.loadManifest(directory);
        } else {
          throw error;
        }
      }

      // Process pending deletions FIRST (before any imports/exports)
      this.emitProgress({
        phase: 'deleting',
        total: 100,
        processed: 15,
        percentage: 15,
        progress: 15,
        message: 'Processing pending deletions...'
      });

      // Record pending deletions and delete physical files immediately
      await this.recordPendingDeletionsInManifest(directory, manifest);

      // Detect changes (after deletions are processed)
      this.emitProgress({
        phase: 'comparing',
        total: 100,
        processed: 20,
        percentage: 20,
        progress: 20,
        message: 'Detecting changes...'
      });
      const changes = await this.changeDetector.compareStates(manifest, directory);

      // Process imports hierarchically (books → folders → notes)
      this.emitProgress({
        phase: 'importing',
        total: 100,
        processed: 30,
        percentage: 30,
        progress: 30,
        message: 'Processing imports...'
      });
      
      // Import books first
      for (const item of changes.toImport.books) {
        try {
          await this.importBook(item, directory);
          result.itemsImported++;
          result.imported.books++;
          const totalItems = changes.toImport.books.length + changes.toImport.folders.length + changes.toImport.notes.length;
          this.emitProgress({
            phase: 'importing',
            total: totalItems,
            processed: result.itemsImported,
            percentage: 30 + (result.itemsImported / totalItems) * 20,
            progress: 30 + (result.itemsImported / totalItems) * 20,
            message: `Imported book: ${item.name || 'Untitled'}`
          });
        } catch (error) {
          result.errors.push(`Failed to import book ${item.name}: ${error.message}`);
        }
      }

      // Import folders
      for (const item of changes.toImport.folders) {
        try {
          await this.importFolder(item, directory);
          result.itemsImported++;
          result.imported.folders++;
          const totalItems = changes.toImport.books.length + changes.toImport.folders.length + changes.toImport.notes.length;
          this.emitProgress({
            phase: 'importing',
            total: totalItems,
            processed: result.itemsImported,
            percentage: 30 + (result.itemsImported / totalItems) * 20,
            progress: 30 + (result.itemsImported / totalItems) * 20,
            message: `Imported folder: ${item.name || 'Untitled'}`
          });
        } catch (error) {
          result.errors.push(`Failed to import folder ${item.name}: ${error.message}`);
        }
      }

      // Import notes
      for (const item of changes.toImport.notes) {
        try {
          await this.importNote(item, directory);
          result.itemsImported++;
          result.imported.notes++;
          const totalItems = changes.toImport.books.length + changes.toImport.folders.length + changes.toImport.notes.length;
          this.emitProgress({
            phase: 'importing',
            total: totalItems,
            processed: result.itemsImported,
            percentage: 30 + (result.itemsImported / totalItems) * 20,
            progress: 30 + (result.itemsImported / totalItems) * 20,
            message: `Imported note: ${item.name || 'Untitled'}`
          });
        } catch (error) {
          result.errors.push(`Failed to import note ${item.name}: ${error.message}`);
        }
      }

      // Load current manifest and merge with database state for exports
      // This preserves deletion records while ensuring parent lookups work correctly
      const existingManifest = await manifestManager.loadManifest(directory);
      const exportManifest = await manifestManager.generateManifestFromDatabase();

      // Preserve deletion records from existing manifest
      exportManifest.deletions = [...existingManifest.deletions];

      // Remove items that are marked as deleted from the items array
      const exportDeletedItemIds = new Set(existingManifest.deletions.map(d => d.id));
      exportManifest.items = exportManifest.items.filter(item => !exportDeletedItemIds.has(item.id));

      // Handle renamed items first using fs.rename() for efficiency
      this.emitProgress({
        phase: 'exporting',
        total: 100,
        processed: 45,
        percentage: 45,
        progress: 45,
        message: 'Processing renamed items...'
      });

      const renamedItems = await this.handleRenamedExports(directory, manifest, exportManifest);

      // Process exports hierarchically (books → folders → notes)
      this.emitProgress({
        phase: 'exporting',
        total: 100,
        processed: 50,
        percentage: 50,
        progress: 50,
        message: 'Processing exports...'
      });

      // Export books first (skip items that were already renamed)
      for (const item of changes.toExport.books) {
        const itemId = `book_${item.id}`;
        if (renamedItems.has(itemId)) {
          console.log(`[UnifiedSyncEngine] Skipping export for renamed book: ${item.name}`);
          result.itemsExported++;
          result.exported.books++;
          continue;
        }

        try {
          await this.exportBook(item, directory, exportManifest);
          result.itemsExported++;
          result.exported.books++;
          const totalExports = changes.toExport.books.length + changes.toExport.folders.length + changes.toExport.notes.length;
          this.emitProgress({
            phase: 'exporting',
            total: totalExports,
            processed: result.itemsExported,
            percentage: 50 + (result.itemsExported / totalExports) * 20,
            progress: 50 + (result.itemsExported / totalExports) * 20,
            message: `Exported book: ${item.name || 'Untitled'}`
          });
        } catch (error) {
          result.errors.push(`Failed to export book ${item.name}: ${error.message}`);
        }
      }

      // Export folders second (skip items that were already renamed)
      for (const item of changes.toExport.folders) {
        const itemId = `folder_${item.id}`;
        if (renamedItems.has(itemId)) {
          console.log(`[UnifiedSyncEngine] Skipping export for renamed folder: ${item.name}`);
          result.itemsExported++;
          result.exported.folders++;
          continue;
        }

        try {
          await this.exportFolder(item, directory, exportManifest);
          result.itemsExported++;
          result.exported.folders++;
          const totalExports = changes.toExport.books.length + changes.toExport.folders.length + changes.toExport.notes.length;
          this.emitProgress({
            phase: 'exporting',
            total: totalExports,
            processed: result.itemsExported,
            percentage: 50 + (result.itemsExported / totalExports) * 20,
            progress: 50 + (result.itemsExported / totalExports) * 20,
            message: `Exported folder: ${item.name || 'Untitled'}`
          });
        } catch (error) {
          result.errors.push(`Failed to export folder ${item.name}: ${error.message}`);
        }
      }

      // Export notes last (skip items that were already renamed)
      for (const item of changes.toExport.notes) {
        const itemId = `note_${item.id}`;
        if (renamedItems.has(itemId)) {
          console.log(`[UnifiedSyncEngine] Skipping export for renamed note: ${item.name}`);
          result.itemsExported++;
          result.exported.notes++;
          continue;
        }

        try {
          await this.exportNote(item, directory, exportManifest);
          result.itemsExported++;
          result.exported.notes++;
          const totalExports = changes.toExport.books.length + changes.toExport.folders.length + changes.toExport.notes.length;
          this.emitProgress({
            phase: 'exporting',
            total: totalExports,
            processed: result.itemsExported,
            percentage: 50 + (result.itemsExported / totalExports) * 20,
            progress: 50 + (result.itemsExported / totalExports) * 20,
            message: `Exported note: ${item.name || 'Untitled'}`
          });
        } catch (error) {
          result.errors.push(`Failed to export note ${item.name}: ${error.message}`);
        }
      }

      // Handle conflicts
      this.emitProgress({
        phase: 'conflicts',
        total: 100,
        processed: 70,
        percentage: 70,
        progress: 70,
        message: 'Resolving conflicts...'
      });
      
      for (const conflict of changes.conflicts) {
        try {
          // Convert LocalItem to ManifestItem for conflict resolution
          const localAsManifest: ManifestItem = {
            id: `${conflict.local.type}_${conflict.local.id}`,
            type: conflict.local.type,
            name: conflict.local.name,
            path: '', // This will be set during resolution
            hash: '',
            modified: conflict.local.modified,
            metadata: {}
          };
          const resolution = await this.conflictResolver.resolveConflict(localAsManifest, conflict.remote, manifest.deviceId);
          result.conflicts.push(conflict);
          
          // Apply resolution
          if (resolution.source === 'local') {
            await this.exportItem(conflict.local, directory, exportManifest);
            result.itemsExported++;
            // Update appropriate export counter
            if (conflict.local.type === 'book') result.exported.books++;
            else if (conflict.local.type === 'folder') result.exported.folders++;
            else if (conflict.local.type === 'note') result.exported.notes++;
          } else if (resolution.source === 'remote') {
            await this.importItem(conflict.remote, directory);
            result.itemsImported++;
            // Update appropriate import counter
            if (conflict.remote.type === 'book') result.imported.books++;
            else if (conflict.remote.type === 'folder') result.imported.folders++;
            else if (conflict.remote.type === 'note') result.imported.notes++;
          }
        } catch (error) {
          result.errors.push(`Failed to resolve conflict for ${conflict.local.name}: ${error.message}`);
        }
      }

      // Process deletions
      if (changes.toDelete && changes.toDelete.length > 0) {
        this.emitProgress({
          phase: 'deleting',
          total: 100,
          processed: 80,
          percentage: 80,
          progress: 80,
          message: 'Processing deletions...'
        });

        let deletedCount = 0;
        for (const item of changes.toDelete) {
          try {
            const numericId = parseInt(item.id);
            if (isNaN(numericId)) {
              throw new Error(`Invalid ID for deletion: ${item.id}`);
            }

            switch (item.type) {
              case 'book':
                await deleteBook(numericId);
                result.deleted.books++;
                console.log(`Deleted book ${item.id} from local database`);
                break;
              case 'folder':
                await deleteFolder(numericId);
                result.deleted.folders++;
                console.log(`Deleted folder ${item.id} from local database`);
                break;
              case 'note':
                await deleteNote(numericId);
                result.deleted.notes++;
                console.log(`Deleted note ${item.id} from local database`);
                break;
              default:
                throw new Error(`Unknown item type: ${item.type}`);
            }
            
            deletedCount++;
            this.emitProgress({
              phase: 'deleting',
              total: changes.toDelete.length,
              processed: deletedCount,
              percentage: 80 + (deletedCount / changes.toDelete.length) * 10,
              progress: 80 + (deletedCount / changes.toDelete.length) * 10,
              message: `Deleted ${item.type} ${item.id}`
            });
          } catch (error) {
            console.error(`Failed to delete ${item.type} ${item.id}:`, error);
            result.errors.push(`Failed to delete ${item.type} ${item.id}: ${error.message}`);
          }
        }
      }

      // Clean up renamed items before updating manifest
      const hasRenames = this.renamedFolders.length > 0 || this.renamedBooks.length > 0 || this.renamedNotes.length > 0;
      if (hasRenames) {
        console.log(`Cleaning up renamed items: ${this.renamedFolders.length} folders, ${this.renamedBooks.length} books, ${this.renamedNotes.length} notes`);
        await this.cleanupRenamedItems();
      }

      // Update manifest
      this.emitProgress({
        phase: 'updating',
        total: 100,
        processed: 90,
        percentage: 90,
        progress: 90,
        message: 'Updating manifest...'
      });

      // Load current manifest to preserve deletion records
      const currentManifest = await manifestManager.loadManifest(directory);

      // Generate fresh manifest from database state
      const populatedManifest = await manifestManager.generateManifestFromDatabase();

      // Preserve existing deletion records from current manifest
      populatedManifest.deletions = [...currentManifest.deletions];

      // Remove items that are marked as deleted from the items array
      const deletedItemIds = new Set(currentManifest.deletions.map(d => d.id));
      populatedManifest.items = populatedManifest.items.filter(item => !deletedItemIds.has(item.id));

      // Pending deletions were already processed at the beginning of sync
      // No need to process them again here

      // Save the updated manifest to disk
      await manifestManager.saveManifest(directory, populatedManifest);
      console.log(`[UnifiedSyncEngine] Saved updated manifest with ${populatedManifest.items.length} items`);

      // Update sync state
      await this.updateSyncState(directory, manifest.lastSync);

      result.success = true;
      result.duration = Date.now() - startTime;

      // Log the complete directory structure that was created
      await this.logDirectoryStructure(directory);

      this.emitProgress({
        phase: 'completed',
        total: 100,
        processed: 100,
        percentage: 100,
        progress: 100,
        message: 'Sync completed successfully!'
      });

    } catch (error) {
      result.errors.push(`Sync failed: ${error.message}`);
      result.duration = Date.now() - startTime;
      
      this.emitProgress({
        phase: 'error',
        total: 100,
        processed: 0,
        percentage: 0,
        progress: 0,
        message: `Sync failed: ${error.message}`
      });
    } finally {
      this.isRunning = false;
      // Clear the sync directory for security
      fileOperations.clearSyncDirectory();
    }

    return result;
  }

  /**
   * Record pending deletions in manifest before regenerating from database
   * @deprecated Use recordPendingDeletionsInManifest instead
   */
  private async recordPendingDeletions(directory: string): Promise<void> {
    const pendingDeletions = databaseHooks.getPendingDeletions();

    if (pendingDeletions.length > 0) {
      console.log(`[UnifiedSyncEngine] Recording ${pendingDeletions.length} pending deletions in manifest`);

      try {
        // Load current manifest to record deletions and get file paths
        const currentManifest = await manifestManager.loadManifest(directory);

        // Record each deletion in the manifest and delete physical files
        for (const deletion of pendingDeletions) {
          // Find the item in manifest to get its path before removing it
          const manifestItem = manifestManager.findItem(currentManifest, deletion.id);

          if (manifestItem) {
            // Delete physical files/folders from sync directory
            await this.deletePhysicalFiles(directory, manifestItem);
          }

          // Record deletion in manifest
          manifestManager.removeItem(currentManifest, deletion.id);
          console.log(`[UnifiedSyncEngine] Recorded deletion of ${deletion.type} ${deletion.id} in manifest`);
        }

        // Save manifest with deletion records
        await manifestManager.saveManifest(directory, currentManifest);

        // Clear pending deletions after recording
        databaseHooks.clearPendingDeletions();

        console.log(`[UnifiedSyncEngine] Successfully recorded ${pendingDeletions.length} deletions in manifest`);
      } catch (error) {
        console.error('[UnifiedSyncEngine] Error recording pending deletions:', error);
        // Don't clear pending deletions if recording failed - they'll be retried next sync
      }
    }
  }

  /**
   * Record pending deletions directly in the provided manifest (more efficient)
   * This avoids the double I/O issue of the old approach
   */
  private async recordPendingDeletionsInManifest(directory: string, manifest: SyncManifest): Promise<void> {
    const pendingDeletions = databaseHooks.getPendingDeletions();

    if (pendingDeletions.length > 0) {
      console.log(`[UnifiedSyncEngine] Recording ${pendingDeletions.length} pending deletions in manifest`);

      try {
        // Load current manifest to get file paths for physical deletion
        const currentManifest = await manifestManager.loadManifest(directory);

        // Record each deletion in the manifest and delete physical files
        for (const deletion of pendingDeletions) {
          // Find the item in current manifest to get its path before removing it
          const manifestItem = manifestManager.findItem(currentManifest, deletion.id);

          if (manifestItem) {
            // Delete physical files/folders from sync directory
            await this.deletePhysicalFiles(directory, manifestItem);

            // Add the item to the fresh manifest first (if not already there)
            const existingItem = manifestManager.findItem(manifest, deletion.id);
            if (!existingItem) {
              manifestManager.addItem(manifest, manifestItem);
            }

            // Now record deletion in the provided manifest
            manifestManager.removeItem(manifest, deletion.id);
            console.log(`[UnifiedSyncEngine] Recorded deletion of ${deletion.type} ${deletion.id} in manifest`);
          } else {
            console.log(`[UnifiedSyncEngine] Item ${deletion.id} not found in current manifest, skipping deletion record`);
          }
        }

        // Save the updated manifest with deletion records
        await manifestManager.saveManifest(directory, manifest);

        // Clear pending deletions after recording
        databaseHooks.clearPendingDeletions();

        console.log(`[UnifiedSyncEngine] Successfully recorded ${pendingDeletions.length} deletions in manifest`);
      } catch (error) {
        console.error('[UnifiedSyncEngine] Error recording pending deletions:', error);
        // Don't clear pending deletions if recording failed - they'll be retried next sync
      }
    }
  }

  /**
   * Delete physical files/folders from sync directory when items are deleted
   */
  private async deletePhysicalFiles(directory: string, manifestItem: any): Promise<void> {
    try {
      const itemPath = path.join(directory, manifestItem.path);

      if (await fileOperations.exists(itemPath)) {
        if (manifestItem.type === 'note') {
          // Delete note file ONLY - DO NOT clean up parent directories
          // Users should explicitly delete folders if they want them removed
          await fs.unlink(itemPath);
          console.log(`[UnifiedSyncEngine] Deleted note file: ${itemPath}`);
        } else if (manifestItem.type === 'folder' || manifestItem.type === 'book') {
          // Delete directory and all contents (only when folder/book is explicitly deleted)
          await fs.rm(itemPath, { recursive: true, force: true });
          console.log(`[UnifiedSyncEngine] Deleted ${manifestItem.type} directory: ${itemPath}`);

          // Only clean up parent directories when a folder/book is explicitly deleted
          // NOT when a note is deleted
          await this.cleanupEmptyParentDirectories(path.dirname(itemPath));
        }
      } else {
        console.log(`[UnifiedSyncEngine] Physical file/folder not found for deletion: ${itemPath}`);
      }
    } catch (error) {
      console.error(`[UnifiedSyncEngine] Error deleting physical files for ${manifestItem.id}:`, error);
      // Continue with manifest recording even if physical deletion fails
    }
  }

  /**
   * Check if a book already exists in the database by ID
   */
  private async bookExistsById(id: number): Promise<Book | null> {
    try {
      return await dbGet<Book>('SELECT * FROM books WHERE id = ?', [id]);
    } catch (error) {
      console.error('Error checking book by ID:', error);
      return null;
    }
  }

  /**
   * Check if a book already exists in the database by title and author
   */
  private async bookExists(title: string, author?: string): Promise<Book | null> {
    const query = author 
      ? `SELECT * FROM books WHERE title = ? AND author = ?`
      : `SELECT * FROM books WHERE title = ?`;
    const params = author ? [title, author] : [title];
    
    try {
      return await dbGet<Book>(query, params);
    } catch (error) {
      console.error('Error checking for existing book:', error);
      return null;
    }
  }

  /**
   * Check if a folder already exists in the database by ID first, then by name
   */
  private async folderExistsById(id: number): Promise<Folder | null> {
    try {
      return await dbGet<Folder>('SELECT * FROM folders WHERE id = ?', [id]);
    } catch (error) {
      console.error('Error checking folder by ID:', error);
      return null;
    }
  }

  /**
   * Check if a folder already exists in the database by name and location
   */
  private async folderExists(name: string, parentId: number | null, bookId: number | null): Promise<Folder | null> {
    let query = `SELECT * FROM folders WHERE name = ?`;
    const params: any[] = [name];
    
    if (parentId !== null) {
      query += ` AND parent_id = ?`;
      params.push(parentId);
    } else {
      query += ` AND parent_id IS NULL`;
    }
    
    if (bookId !== null) {
      query += ` AND book_id = ?`;
      params.push(bookId);
    } else {
      query += ` AND book_id IS NULL`;
    }
    
    try {
      return await dbGet<Folder>(query, params);
    } catch (error) {
      console.error('Error checking for existing folder:', error);
      return null;
    }
  }

  /**
   * Check if a note already exists in the database by ID
   */
  private async noteExistsById(id: number): Promise<Note | null> {
    try {
      return await dbGet<Note>('SELECT * FROM notes WHERE id = ?', [id]);
    } catch (error) {
      console.error('Error checking note by ID:', error);
      return null;
    }
  }

  /**
   * Check if a note already exists in the database by title and location
   */
  private async noteExists(title: string, folderId: number | null, bookId: number | null): Promise<Note | null> {
    let query = `SELECT * FROM notes WHERE title = ?`;
    const params: any[] = [title];
    
    if (folderId !== null) {
      query += ` AND folder_id = ?`;
      params.push(folderId);
    } else {
      query += ` AND folder_id IS NULL`;
    }
    
    if (bookId !== null) {
      query += ` AND book_id = ?`;
      params.push(bookId);
    } else {
      query += ` AND book_id IS NULL`;
    }
    
    try {
      return await dbGet<Note>(query, params);
    } catch (error) {
      console.error('Error checking for existing note:', error);
      return null;
    }
  }

  /**
   * Import a book from the sync directory
   */
  private async importBook(item: ManifestItem, directory: string): Promise<void> {
    // For cover import, we need to use the actual backup folder structure, not the manifest path
    // The manifest path may have collision suffixes (_2, _3, etc.) but the backup uses the original name
    const actualBookPath = path.join(directory, 'Books', sanitizeBookTitle(item.name));

    // Validate and enforce Books folder structure for other operations
    let bookPath: string;
    if (item.path.startsWith('Books/')) {
      bookPath = path.join(directory, item.path);
    } else {
      console.warn(`[ImportBook] Book "${item.name}" has invalid path "${item.path}", correcting to Books folder`);
      // Use sanitizeBookTitle to ensure consistent naming with export
      bookPath = path.join(directory, 'Books', sanitizeBookTitle(item.name));
    }
    
    // Extract metadata from manifest item (now contains all book data)
    const metadata = item.metadata || {};
    
    // Wrap all database operations in a transaction
    await withTransaction(async () => {
      // CRITICAL FIX: Don't use manifest ID numbers for database lookups
      // Only check if we've already imported this manifest item in THIS sync session
      let existingBook: Book | null = null;
      
      const mappedId = this.importIdMapping.get(item.id);
      if (mappedId) {
        // We've already imported this item in this sync session
        existingBook = await this.bookExistsById(mappedId);
      }
      
      // If not already imported, check by title and author for backwards compatibility
      if (!existingBook) {
        existingBook = await this.bookExists(item.name, metadata.author);
      }
      
      let book: Book;
      if (existingBook) {
        console.log(`Book "${item.name}" already exists with ID ${existingBook.id}, updating`);
        
        // Track if book was renamed
        if (existingBook.title !== item.name) {
          console.log(`Detected book rename: "${existingBook.title}" -> "${item.name}"`);
          const oldBookPath = path.join(directory, sanitizeBookTitle(existingBook.title));
          const newBookPath = bookPath;
          this.renamedBooks.push({ oldPath: oldBookPath, newPath: newBookPath });
        }
        
        // Update existing book (including title if it was renamed) - use database field names
        await updateBook(existingBook.id!, {
          title: item.name,
          author: metadata.author || existingBook.author,
          isbn: metadata.isbn || existingBook.isbn,
          publication_date: metadata.publication_date || existingBook.publication_date,
          description: metadata.description || existingBook.description,
          page_count: metadata.page_count || existingBook.page_count,
          current_page: metadata.current_page || existingBook.current_page,
          rating: metadata.rating || existingBook.rating,
          status: (metadata.status || existingBook.status) as 'to-read' | 'reading' | 'completed',
          olid: metadata.olid || existingBook.olid,
          cover_url: metadata.cover_url || existingBook.cover_url,
          language: metadata.language || existingBook.language,
          genres: metadata.genres || existingBook.genres,
          custom_fields: metadata.custom_fields || existingBook.custom_fields
        });
        book = { ...existingBook, ...metadata };
        book.id = existingBook.id;
      } else {
        // Create new book - use database field names
        book = await createBook({
          title: item.name,
          author: metadata.author || '',
          isbn: metadata.isbn,
          publication_date: metadata.publication_date,
          description: metadata.description,
          page_count: metadata.page_count,
          current_page: metadata.current_page,
          rating: metadata.rating,
          status: metadata.status as 'to-read' | 'reading' | 'completed',
          olid: metadata.olid,
          cover_url: metadata.cover_url,
          language: metadata.language,
          genres: metadata.genres,
          custom_fields: metadata.custom_fields
        });
      }

      // Phase 3: Track sync ID to local ID mapping for import relationships
      this.importIdMapping.set(item.id, book.id!);

      // Handle cover image if present (now stored as hidden file)
      if (metadata.coverImage) {
        // Use the actual backup folder path, not the manifest path (which may have collision suffixes)
        const coverPath = path.join(actualBookPath, metadata.coverImage);
        console.log(`[DEBUG] Import cover for book "${book.title}" (ID: ${book.id}) - Cover path: ${coverPath}`);
        try {
          if (await fileOperations.exists(coverPath)) {
            console.log(`[DEBUG] Cover file exists, importing...`);
            const coverBuffer = await fileOperations.readFileBuffer(coverPath);
            const mediaFile = await saveMediaFile(
              null,
              coverBuffer,
              metadata.coverImage,
              'image/jpeg',
              book.id,
              true
            );
            console.log(`[DEBUG] Cover saved to media_files table with ID: ${mediaFile.id}`);

            // Update book with cover URL
            await updateBook(book.id!, {
              cover_url: filePathToMediaUrl(mediaFile.file_path)
            });
            console.log(`[DEBUG] Book cover_url updated successfully`);
          } else {
            console.log(`[DEBUG] Cover file does not exist at path: ${coverPath}`);
          }
        } catch (error) {
          console.error(`Failed to import cover for book ${book.id}:`, error);
          // Continue with import even if cover fails
        }
      } else {
        console.log(`[DEBUG] No coverImage metadata found for book "${book.title}"`);
      }

      // Phase 3: Removed recordSyncItem - items are already in manifest during import
    });
  }

  /**
   * Import a folder from the sync directory
   */
  private async importFolder(item: ManifestItem, directory: string): Promise<void> {
    // Validate folder path - if it has a book relationship, it should be under Books/
    let folderPath: string;
    if (item.relationships?.bookId && !item.path.startsWith('Books/')) {
      console.warn(`[ImportFolder] Folder "${item.name}" has book relationship but invalid path "${item.path}", correcting to Books folder structure`);
      // For book folders, ensure they're under the Books directory
      folderPath = path.join(directory, 'Books', sanitizeFolderName(item.name));
    } else {
      folderPath = path.join(directory, item.path);
    }
    const folderName = item.name || path.basename(folderPath);
    
    // Find parent folder ID if nested
    let parentId: number | null = null;
    let bookId: number | null = null;
    
    // Extract metadata from manifest item
    const metadata = item.metadata || {};
    
    // Determine parent from manifest relationships
    if (item.relationships) {
      // Get book ID from relationships using import mapping
      if (item.relationships.bookId) {
        // CRITICAL FIX: Validate that folders with book relationships are actually under Books/ in the manifest
        if (!item.path.startsWith('Books/')) {
          console.warn(`[ImportFolder] Folder "${item.name}" has book relationship but not under Books/ in manifest path "${item.path}", clearing relationship`);
          // Clear the incorrect relationship to prevent corruption
          bookId = null;
        } else {
          const localBookId = this.importIdMapping.get(item.relationships.bookId);
          if (localBookId) {
            bookId = localBookId;
          }
        }
      }

      // Get parent folder ID from relationships using import mapping
      if (item.relationships.parentId) {
        const localParentId = this.importIdMapping.get(item.relationships.parentId);
        if (localParentId) {
          parentId = localParentId;
        }
      }
    }

    // CRITICAL FIX: If folder has a book relationship but no parent, it should be under the Books folder
    if (bookId && parentId === null) {
      console.log(`[ImportFolder] Folder "${item.name}" has book relationship but no parent, setting parent to Books folder`);
      // Find the Books folder (it should always exist)
      try {
        const booksFolder = await dbGet<Folder>('SELECT * FROM folders WHERE name = ? AND parent_id IS NULL', ['Books']);
        if (booksFolder) {
          parentId = booksFolder.id!;
          console.log(`[ImportFolder] Set parent to Books folder (ID: ${parentId})`);
        } else {
          console.error(`[ImportFolder] Books folder not found! This should never happen.`);
        }
      } catch (error) {
        console.error(`[ImportFolder] Error finding Books folder:`, error);
      }
    }

    // Wrap database operations in a transaction
    await withTransaction(async () => {
      // CRITICAL FIX: Don't use manifest ID numbers for database lookups
      // Only check if we've already imported this manifest item in THIS sync session
      let existingFolder: Folder | null = null;
      
      const mappedId = this.importIdMapping.get(item.id);
      if (mappedId) {
        // We've already imported this item in this sync session
        existingFolder = await this.folderExistsById(mappedId);
      }
      
      // If not already imported, check by name and location for backwards compatibility
      if (!existingFolder) {
        existingFolder = await this.folderExists(folderName, parentId, bookId);
      }
      
      let folder: Folder;
      if (existingFolder) {
        console.log(`Folder "${folderName}" already exists with ID ${existingFolder.id}, updating`);
        // Update existing folder (including name if it was renamed)
        await updateFolder(existingFolder.id!, {
          name: folderName,
          color: metadata.color || existingFolder.color,
          parent_id: parentId,
          book_id: bookId
        });
        folder = existingFolder;
        
        // If the folder was renamed, track it for cleanup
        if (existingFolder.name !== folderName) {
          console.log(`Detected folder rename: "${existingFolder.name}" -> "${folderName}"`);
          // Build the old and new paths for cleanup
          const oldFolderPath = await this.buildFolderPath(existingFolder, directory);
          const newFolderPath = path.join(directory, item.path);
          this.renamedFolders.push({ oldPath: oldFolderPath, newPath: newFolderPath });
        }
      } else {
        // Create new folder
        folder = await createFolder({
          name: folderName,
          parent_id: parentId,
          book_id: bookId,
          color: metadata.color || null
        });
      }

      // Phase 3: Track sync ID to local ID mapping for import relationships
      this.importIdMapping.set(item.id, folder.id!);
    });
  }

  /**
   * Import a note from the sync directory (.noti format)
   */
  private async importNote(item: ManifestItem, directory: string): Promise<void> {
    // Validate note path - if it has a book relationship but path doesn't start with Books/, correct it
    let notePath: string;
    if (item.relationships?.bookId && !item.path.startsWith('Books/')) {
      console.warn(`[ImportNote] Note "${item.name}" has book relationship but invalid path "${item.path}", attempting to correct`);
      // Try to build the correct path based on relationships
      if (item.relationships.folderId) {
        // Note is in a folder - we'll need to wait for the folder to be imported first
        // For now, use the original path and let the relationship resolution handle it
        notePath = path.join(directory, item.path);
      } else {
        // Note is directly in a book folder
        const bookTitle = item.relationships.bookId.replace('book_', ''); // This is a simplification
        notePath = path.join(directory, 'Books', sanitizeBookTitle(bookTitle), `${sanitizeNoteTitle(item.name)}.noti`);
      }
    } else {
      notePath = path.join(directory, item.path);
    }

    // Read and parse .noti file
    const notiData = await fileOperations.readNote(notePath);

    // Extract content and metadata from .noti file
    const htmlContent = notiData.content.html;
    const markdownContent = notiData.content.markdown; // This is just preview text
    const metadata = notiData.metadata || {};

    // Determine parent folder/book from manifest relationships
    let folderId: number | null = null;
    let bookId: number | null = null;
    
    if (item.relationships) {
      // Get book ID from relationships using import mapping
      if (item.relationships.bookId) {
        // CRITICAL FIX: Validate that notes with book relationships are actually under Books/ in the manifest
        if (!item.path.startsWith('Books/')) {
          console.warn(`[ImportNote] Note "${item.name}" has book relationship but not under Books/ in manifest path "${item.path}", clearing relationship`);
          // Clear the incorrect relationship to prevent corruption
          bookId = null;
        } else {
          const localBookId = this.importIdMapping.get(item.relationships.bookId);
          if (localBookId) {
            bookId = localBookId;
          }
        }
      }

      // Get folder ID from relationships using import mapping
      if (item.relationships.folderId) {
        const localFolderId = this.importIdMapping.get(item.relationships.folderId);
        if (localFolderId) {
          folderId = localFolderId;
        }
      }
    }

    // Wrap database operations in a transaction
    await withTransaction(async () => {
      const noteTitle = item.name || path.basename(notePath, '.noti');

      // CRITICAL FIX: Don't use manifest ID numbers for database lookups
      // Only check if we've already imported this manifest item in THIS sync session
      let existingNote: Note | null = null;

      const mappedId = this.importIdMapping.get(item.id);
      if (mappedId) {
        // We've already imported this item in this sync session
        existingNote = await this.noteExistsById(mappedId);
      }

      // If not already imported, check by title and location for backwards compatibility
      if (!existingNote) {
        existingNote = await this.noteExists(noteTitle, folderId, bookId);
      }

      let note: Note;
      if (existingNote) {
        console.log(`Note "${noteTitle}" already exists with ID ${existingNote.id}, updating`);

        // Track if note was renamed
        if (existingNote.title !== noteTitle) {
          console.log(`Detected note rename: "${existingNote.title}" -> "${noteTitle}"`);
          // Build the old path for the note
          const oldNotePath = await this.buildNotePath(existingNote, directory);
          this.renamedNotes.push({ oldPath: oldNotePath, newPath: notePath });
        }

        // Update existing note with imported content
        await updateNote(existingNote.id!, {
          title: noteTitle,
          content: markdownContent, // Store preview text as markdown content
          html_content: null, // Will be set after media restoration
          folder_id: folderId,
          book_id: bookId,
          type: metadata.type || existingNote.type,
          color: metadata.color || existingNote.color,
          last_viewed_at: metadata.last_viewed_at || existingNote.last_viewed_at
        });
        note = existingNote;
      } else {
        // Create new note
        note = await createNote({
          title: noteTitle,
          content: markdownContent, // Store preview text as markdown content
          html_content: null, // Will be set after media restoration
          folder_id: folderId,
          book_id: bookId,
          type: metadata.type || 'text',
          color: metadata.color || null,
          last_viewed_at: metadata.last_viewed_at
        });
      }

      // Process embedded media if present
      if (notiData.media && notiData.media.length > 0) {
        const restoredHtmlContent = await restoreEmbeddedMedia(
          htmlContent,
          notiData.media,
          note.id!
        );

        // Update note with restored HTML content
        await updateNote(note.id!, {
          html_content: restoredHtmlContent
        });

        console.log(`[UnifiedSyncEngine] Restored ${notiData.media.length} media files for note "${noteTitle}"`);
      } else {
        // No media, just set the HTML content
        await updateNote(note.id!, {
          html_content: htmlContent
        });
      }

      // Phase 3: Track sync ID to local ID mapping for import relationships
      this.importIdMapping.set(item.id, note.id!);
    });
  }

  /**
   * Export a book to the sync directory
   */
  private async exportBook(item: LocalItem, directory: string, manifest: SyncManifest): Promise<void> {
    const book = await getBookById(item.id);
    const bookPath = path.join(directory, 'Books', sanitizeBookTitle(book.title));
    
    // Ensure book directory exists
    await fileOperations.ensurePath(bookPath);
    
    // Create book metadata for manifest (enhanced to replace .book-meta.json) - use database field names
    const bookMeta: any = {
      id: book.id,
      title: book.title,
      author: book.author,
      isbn: book.isbn,
      publication_date: book.publication_date,
      description: book.description,
      page_count: book.page_count,
      current_page: book.current_page,
      rating: book.rating,
      status: book.status,
      olid: book.olid,
      cover_url: book.cover_url,
      language: book.language,
      genres: book.genres,
      custom_fields: book.custom_fields,
      created_at: book.created_at,
      updated_at: book.updated_at
    };

    // Handle cover export - save as hidden .cover.jpg file in book folder
    // Query media_files directly instead of relying on cover_url (which is set to null after processing)
    const mediaQuery = `SELECT * FROM media_files WHERE book_id = ? AND is_cover = 1`;
    const mediaFile = await dbGet<any>(mediaQuery, [book.id]);

    if (mediaFile) {
      const coverFileName = '.cover.jpg';
      const coverPath = path.join(bookPath, coverFileName);

      try {
        // Copy cover file - use readFileBuffer for media files
        const sourceData = await fileOperations.readFileBuffer(mediaFile.file_path);
        await fileOperations.writeFileBuffer(coverPath, sourceData);

        // Mark that cover exists in metadata
        (bookMeta as any).coverImage = coverFileName;
      } catch (error) {
        console.error(`Failed to export cover for book ${book.id}:`, error);
        // Continue with export even if cover fails
      }
    }
    
    // Update manifest with exported book (metadata stored in manifest, not separate file)
    manifestManager.updateManifestWithExport(manifest, {
      ...book,
      type: 'book',
      name: book.title
    }, path.relative(directory, bookPath), bookMeta);
    
    // Phase 3: Removed recordSyncItem - sync state now tracked in manifest only
  }

  /**
   * Export a folder to the sync directory
   */
  private async exportFolder(item: LocalItem, directory: string, manifest: SyncManifest): Promise<void> {
    const folder = await getFolderById(item.id);
    
    // Find the folder's path from the manifest (already calculated based on parent_id chain)
    const folderId = `folder_${folder.id}`;
    const folderItem = manifest.items.find(item => item.id === folderId);
    
    let folderPath: string;
    if (folderItem && folderItem.path) {
      // Use the pre-calculated path from manifest (based on parent_id chain)
      folderPath = path.join(directory, folderItem.path);
    } else {
      // Fallback - this shouldn't happen with proper manifest generation
      console.warn(`Folder ${folderId} not found in manifest, using fallback path`);
      folderPath = path.join(directory, sanitizeFolderName(folder.name));
    }

    // Create folder
    await fileOperations.ensurePath(folderPath);
    
    // Update manifest with exported folder
    const relativePath = path.relative(directory, folderPath);
    manifestManager.updateManifestWithExport(manifest, {
      ...folder,
      type: 'folder'
    }, relativePath);
    
    // Phase 3: Removed recordSyncItem - sync state now tracked in manifest only
  }

  /**
   * Export a note to the sync directory in .noti format
   */
  private async exportNote(item: LocalItem, directory: string, manifest: SyncManifest): Promise<void> {
    const note = await getNoteById(item.id);

    // Notes always go in their folder_id location (as per database structure)
    let notePath: string;
    if (note.folder_id) {
      // Find folder in manifest to get its path
      const folderId = `folder_${note.folder_id}`;
      const folderItem = manifest.items.find(item => item.id === folderId);
      if (folderItem && folderItem.path) {
        // Place note in its folder - change extension to .noti
        notePath = path.join(directory, folderItem.path, `${sanitizeNoteTitle(note.title)}.noti`);
      } else {
        // Fallback - create orphaned folder path to maintain structure
        console.warn(`Folder ${folderId} not found in manifest for note ${note.id}. Creating orphaned folder path.`);
        const orphanedPath = path.join(directory, '_orphaned_folders', folderId);
        await fileOperations.ensurePath(orphanedPath);
        notePath = path.join(orphanedPath, `${sanitizeNoteTitle(note.title)}.noti`);
      }
    } else {
      // Standalone note at root - change extension to .noti
      notePath = path.join(directory, `${sanitizeNoteTitle(note.title)}.noti`);
    }

    // Get embedded media for the note
    const embeddedMedia = await embedMediaFiles(note.id!);

    // Process HTML content to replace media URLs with embedded references
    let processedHtmlContent = note.html_content || '';
    if (embeddedMedia.length > 0) {
      // Get media files to map URLs to embedded references
      const mediaFiles = await getMediaFilesByNoteId(note.id!);
      processedHtmlContent = replaceMediaUrlsWithEmbedded(
        processedHtmlContent,
        mediaFiles,
        embeddedMedia
      );
    }

    // Create markdown preview (first 50 words from content or title)
    const markdownPreview = createMarkdownPreview(note.content || note.title);

    // Extract plain text for search
    const plainText = extractPlainText(processedHtmlContent);

    // Create .noti file structure
    const notiData: NotiFileData = {
      version: "1.0",
      type: "noti-note",
      schema: "https://noti.app/schemas/note/v1.0",
      metadata: {
        id: note.id!,
        title: note.title,
        created_at: note.created_at,
        updated_at: note.updated_at,
        last_viewed_at: note.last_viewed_at,
        type: note.type || 'text',
        color: note.color,
        folder_id: note.folder_id,
        book_id: note.book_id
      },
      content: {
        html: processedHtmlContent,
        markdown: markdownPreview,
        plain_text: plainText
      },
      media: embeddedMedia
    };

    // Write .noti file
    await fileOperations.writeNote(notePath, notiData);

    // Update manifest with exported note
    const relativePath = path.relative(directory, notePath);
    manifestManager.updateManifestWithExport(manifest, {
      ...note,
      type: 'note',
      name: note.title
    }, relativePath);

    console.log(`[UnifiedSyncEngine] Exported note "${note.title}" with ${embeddedMedia.length} embedded media files`);
  }

  /**
   * Generic item import dispatcher
   */
  private async importItem(item: ManifestItem, directory: string): Promise<void> {
    switch (item.type) {
      case ItemType.BOOK:
        return this.importBook(item, directory);
      case ItemType.FOLDER:
        return this.importFolder(item, directory);
      case ItemType.NOTE:
        return this.importNote(item, directory);
      default:
        throw new Error(`Unknown item type: ${item.type}`);
    }
  }

  /**
   * Generic item export dispatcher
   */
  private async exportItem(item: LocalItem, directory: string, manifest: SyncManifest): Promise<void> {
    switch (item.type) {
      case ItemType.BOOK:
        return this.exportBook(item, directory, manifest);
      case ItemType.FOLDER:
        return this.exportFolder(item, directory, manifest);
      case ItemType.NOTE:
        return this.exportNote(item, directory, manifest);
      default:
        throw new Error(`Unknown item type: ${item.type}`);
    }
  }

  /**
   * Update sync state in database
   */
  private async updateSyncState(directory: string, hash: string): Promise<void> {
    await withTransaction(async () => {
      // Create sync_directory_state table if it doesn't exist
      await dbRun(`
        CREATE TABLE IF NOT EXISTS sync_directory_state (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          directory TEXT UNIQUE NOT NULL,
          last_sync_hash TEXT,
          last_sync_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Update or insert sync state
      await dbRun(`
        INSERT INTO sync_directory_state (directory, last_sync_hash, updated_at)
        VALUES (?, ?, CURRENT_TIMESTAMP)
        ON CONFLICT(directory) DO UPDATE SET
          last_sync_hash = excluded.last_sync_hash,
          last_sync_at = CURRENT_TIMESTAMP,
          updated_at = CURRENT_TIMESTAMP
      `, [directory, hash]);
    });
  }

  // Database sync methods removed in Phase 3 - using manifest-only approach
  // The following methods were removed:
  // - recordSyncItem: Previously tracked items in sync_items table
  // - getSyncItemByPath: Previously looked up items by path
  // - getSyncItemById: Previously looked up items by ID
  // All sync state is now tracked in .sync-manifest.json

  /**
   * Log the complete directory structure of the sync directory
   */
  private async logDirectoryStructure(directory: string): Promise<void> {
    try {
      console.log('\n=== SYNC DIRECTORY STRUCTURE ===');
      console.log(`Root: ${directory}`);
      console.log('-'.repeat(50));
      
      const structure = await this.buildDirectoryTree(directory, '', true);
      console.log(structure);
      console.log('-'.repeat(50));
      console.log('=== END SYNC DIRECTORY STRUCTURE ===\n');
    } catch (error) {
      console.error('Failed to log directory structure:', error.message);
    }
  }

  /**
   * Recursively build directory tree structure
   */
  private async buildDirectoryTree(dirPath: string, prefix: string = '', isLast: boolean = true): Promise<string> {
    try {
      const entries = await fs.readdir(dirPath, { withFileTypes: true });
      let result = '';
      
      // Sort entries: directories first, then files, alphabetically within each group
      const sortedEntries = entries.sort((a, b) => {
        if (a.isDirectory() && !b.isDirectory()) return -1;
        if (!a.isDirectory() && b.isDirectory()) return 1;
        return a.name.localeCompare(b.name);
      });

      for (let i = 0; i < sortedEntries.length; i++) {
        const entry = sortedEntries[i];
        const isLastEntry = i === sortedEntries.length - 1;
        const connector = isLastEntry ? '+-- ' : '+-- ';
        const nextPrefix = prefix + (isLastEntry ? '    ' : '|   ');
        
        if (entry.isDirectory()) {
          result += `${prefix}${connector}[DIR] ${entry.name}/\n`;
          const subPath = path.join(dirPath, entry.name);
          result += await this.buildDirectoryTree(subPath, nextPrefix, isLastEntry);
        } else {
          // Add file type indicators based on extension
          const fileType = this.getFileType(entry.name);
          result += `${prefix}${connector}${fileType} ${entry.name}\n`;
        }
      }
      
      return result;
    } catch (error) {
      return `${prefix}+-- [ERROR] Error reading directory: ${error.message}\n`;
    }
  }

  /**
   * Get appropriate file type label based on extension
   */
  private getFileType(filename: string): string {
    const ext = path.extname(filename).toLowerCase();
    const basename = path.basename(filename, ext);
    
    // Special sync files
    if (filename === '.sync-manifest.json') return '[MANIFEST]';
    if (filename === '.book-meta.json') return '[BOOK-META]';
    if (filename.endsWith('.noti.json')) return '[NOTE-META]';
    
    // File extensions
    switch (ext) {
      case '.md': return '[MARKDOWN]';
      case '.noti': return '[NOTI-NOTE]';
      case '.json': return '[JSON]';
      case '.jpg':
      case '.jpeg':
      case '.png':
      case '.gif':
      case '.webp': return '[IMAGE]';
      case '.pdf': return '[PDF]';
      case '.txt': return '[TEXT]';
      case '.zip': return '[ZIP]';
      default: return '[FILE]';
    }
  }

  /**
   * Build folder path recursively for an existing folder
   */
  private async buildFolderPath(folder: Folder, baseDirectory: string): Promise<string> {
    const pathParts: string[] = [];
    let currentFolder: Folder | null = folder;
    
    // Build path from folder up to root
    while (currentFolder) {
      pathParts.unshift(sanitizeFolderName(currentFolder.name));
      
      if (currentFolder.parent_id) {
        currentFolder = await getFolderById(currentFolder.parent_id);
      } else if (currentFolder.book_id) {
        // Add book folder
        const book = await getBookById(currentFolder.book_id);
        if (book) {
          pathParts.unshift(sanitizeBookTitle(book.title));
        }
        currentFolder = null;
      } else {
        currentFolder = null;
      }
    }
    
    return path.join(baseDirectory, ...pathParts);
  }

  /**
   * Build note path for an existing note
   */
  private async buildNotePath(note: Note, baseDirectory: string): Promise<string> {
    const noteName = `${sanitizeNoteTitle(note.title)}.noti`;
    
    if (note.folder_id) {
      // Note is in a folder - build the folder path first
      const folder = await getFolderById(note.folder_id);
      if (folder) {
        const folderPath = await this.buildFolderPath(folder, baseDirectory);
        return path.join(folderPath, noteName);
      }
    } else if (note.book_id) {
      // Note is directly in a book
      const book = await getBookById(note.book_id);
      if (book) {
        const bookPath = path.join(baseDirectory, sanitizeBookTitle(book.title));
        return path.join(bookPath, noteName);
      }
    }
    
    // Standalone note at root
    return path.join(baseDirectory, noteName);
  }

  /**
   * Clean up old paths after renames for all item types
   */
  private async cleanupRenamedItems(): Promise<void> {
    // Clean up renamed folders (directories)
    for (const { oldPath, newPath } of this.renamedFolders) {
      try {
        const oldExists = await fileOperations.exists(oldPath);
        const newExists = await fileOperations.exists(newPath);
        
        if (oldExists && newExists && oldPath !== newPath) {
          console.log(`Cleaning up renamed folder: ${oldPath}`);
          await fs.rm(oldPath, { recursive: true, force: true });
          await this.cleanupEmptyParentDirectories(path.dirname(oldPath));
        }
      } catch (error) {
        console.error(`Error cleaning up renamed folder ${oldPath}:`, error);
      }
    }
    
    // Clean up renamed books (directories)
    for (const { oldPath, newPath } of this.renamedBooks) {
      try {
        const oldExists = await fileOperations.exists(oldPath);
        const newExists = await fileOperations.exists(newPath);
        
        if (oldExists && newExists && oldPath !== newPath) {
          console.log(`Cleaning up renamed book: ${oldPath}`);
          await fs.rm(oldPath, { recursive: true, force: true });
          await this.cleanupEmptyParentDirectories(path.dirname(oldPath));
        }
      } catch (error) {
        console.error(`Error cleaning up renamed book ${oldPath}:`, error);
      }
    }
    
    // Clean up renamed notes (files)
    for (const { oldPath, newPath } of this.renamedNotes) {
      try {
        const oldExists = await fileOperations.exists(oldPath);
        const newExists = await fileOperations.exists(newPath);
        
        if (oldExists && newExists && oldPath !== newPath) {
          console.log(`Cleaning up renamed note: ${oldPath}`);
          await fs.unlink(oldPath);
          await this.cleanupEmptyParentDirectories(path.dirname(oldPath));
        }
      } catch (error) {
        console.error(`Error cleaning up renamed note ${oldPath}:`, error);
      }
    }
    
    // Clear all lists after cleanup
    this.renamedFolders = [];
    this.renamedBooks = [];
    this.renamedNotes = [];
  }

  /**
   * Recursively clean up empty parent directories with smart protection for critical folders
   */
  private async cleanupEmptyParentDirectories(dirPath: string): Promise<void> {
    try {
      // Don't clean up the sync root directory
      if (!dirPath || dirPath === '/' || dirPath === '.' || dirPath === '..') {
        return;
      }

      // SMART PROTECTION: Get the sync root directory from fileOperations
      const syncRoot = fileOperations.getSyncDirectory();
      if (!syncRoot) {
        console.warn(`[UnifiedSyncEngine] Sync directory not set, cannot determine protected folders`);
        return;
      }

      // Calculate relative path from sync root
      const normalizedPath = path.normalize(dirPath);
      const normalizedSyncRoot = path.normalize(syncRoot);

      // Ensure we're working within the sync directory
      if (!normalizedPath.startsWith(normalizedSyncRoot)) {
        console.warn(`[UnifiedSyncEngine] Path ${dirPath} is outside sync directory, skipping cleanup`);
        return;
      }

      const relativePath = path.relative(normalizedSyncRoot, normalizedPath);

      // Protect critical root folders - these should NEVER be deleted
      const criticalRootFolders = ['Books', 'Notes', 'Folders', 'Media'];
      if (criticalRootFolders.includes(relativePath)) {
        console.log(`[UnifiedSyncEngine] Protected critical system folder detected, skipping cleanup: ${relativePath}`);
        return;
      }

      // Additional protection: Don't delete if this is the direct Books folder path
      if (relativePath === 'Books' || relativePath === 'Books/' || relativePath.endsWith('/Books') || relativePath.endsWith('/Books/')) {
        console.log(`[UnifiedSyncEngine] Books folder protection triggered, skipping cleanup: ${relativePath}`);
        return;
      }

      const files = await fs.readdir(dirPath);

      // If directory is empty, remove it and check parent
      if (files.length === 0) {
        await fs.rmdir(dirPath);
        console.log(`[UnifiedSyncEngine] Removed empty directory: ${dirPath} (relative: ${relativePath})`);

        // Recursively check parent directory
        const parentDir = path.dirname(dirPath);
        await this.cleanupEmptyParentDirectories(parentDir);
      }
    } catch (error) {
      // Ignore errors (directory might not exist or have permissions issues)
      console.debug(`[UnifiedSyncEngine] Could not clean up directory ${dirPath}:`, error.message);
    }
  }

  /**
   * Handle renamed items by using fs.rename() to move files/directories directly
   * This prevents duplicate files from being created during export
   */
  private async handleRenamedExports(
    directory: string,
    oldManifest: SyncManifest,
    newManifest: SyncManifest
  ): Promise<Set<string>> {
    const renamedItems = new Set<string>();

    console.log(`[UnifiedSyncEngine] Checking for renamed items...`);

    // Compare old manifest with new database state to detect renames
    for (const newItem of newManifest.items) {
      const oldItem = oldManifest.items.find(item => item.id === newItem.id);

      if (oldItem && oldItem.path !== newItem.path) {
        // Item was renamed - move the file/folder directly
        const oldPath = path.join(directory, oldItem.path);
        const newPath = path.join(directory, newItem.path);

        try {
          console.log(`[UnifiedSyncEngine] Detected rename: ${newItem.type} "${oldItem.name}" -> "${newItem.name}"`);
          console.log(`[UnifiedSyncEngine] Moving: ${oldPath} -> ${newPath}`);

          // Check if source exists and target doesn't exist
          const sourceExists = await fileOperations.exists(oldPath);
          const targetExists = await fileOperations.exists(newPath);

          if (sourceExists && !targetExists) {
            if (newItem.type === 'note') {
              await fileOperations.renameFile(oldPath, newPath);
            } else {
              // Books and folders are directories
              await fileOperations.renameDirectory(oldPath, newPath);
            }

            renamedItems.add(newItem.id);
            console.log(`[UnifiedSyncEngine] Successfully renamed ${newItem.type}: ${oldItem.name} -> ${newItem.name}`);
          } else if (!sourceExists) {
            console.warn(`[UnifiedSyncEngine] Source path does not exist for rename: ${oldPath}`);
          } else if (targetExists) {
            console.warn(`[UnifiedSyncEngine] Target path already exists for rename: ${newPath}`);
          }
        } catch (error) {
          console.error(`[UnifiedSyncEngine] Failed to rename ${newItem.type} ${newItem.id}:`, error);
          // Don't throw - let the regular export process handle this item
        }
      }
    }

    if (renamedItems.size > 0) {
      console.log(`[UnifiedSyncEngine] Successfully processed ${renamedItems.size} renamed items using fs.rename()`);
    } else {
      console.log(`[UnifiedSyncEngine] No renamed items detected`);
    }

    return renamedItems;
  }

  /**
   * Emit progress event
   */
  private emitProgress(progress: SyncProgress): void {
    this.emit('progress', progress);
  }
}

// Export singleton instance
export const unifiedSyncEngine = new UnifiedSyncEngine();