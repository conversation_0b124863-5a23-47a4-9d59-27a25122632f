import * as crypto from 'crypto';
import * as path from 'path';
import { 
  SyncManifest, 
  ManifestItem, 
  DeletionRecord,
  LocalItem 
} from './types';
import { fileOperations } from './file-operations';
import { getSetting, setSetting } from '../settings-api';
import { dbAll, withReadTransaction } from '../../database/database-api';

export class ManifestManager {
  private static MANIFEST_FILENAME = '.sync-manifest.json';
  private static MANIFEST_VERSION = 1;
  private deviceId: string | null = null;
  private pathCollisionMap: Map<string, Set<string>> = new Map();
  private folderMap: Map<number, any> = new Map();
  private folderPaths: Map<number, string> = new Map();

  /**
   * Load manifest from sync directory
   */
  async loadManifest(directory: string): Promise<SyncManifest> {
    try {
      const manifestPath = path.join(directory, ManifestManager.MANIFEST_FILENAME);

      if (await fileOperations.exists(manifestPath)) {
        const content = await fileOperations.readFileAtomic(manifestPath);
        return JSON.parse(content) as SyncManifest;
      }

      // CRITICAL: Don't create manifest here - this should only happen when backup is enabled
      // Throw error if manifest doesn't exist during sync operations
      throw new Error(`No manifest found in sync directory: ${directory}. Please initialize backup first.`);
    } catch (error) {
      console.error('Failed to load manifest:', error);
      throw new Error(`Cannot proceed with sync: Failed to load manifest - ${error.message}`);
    }
  }

  /**
   * Initialize backup location - creates manifest only when backup is first enabled
   */
  async initializeBackupLocation(backupPath: string): Promise<void> {
    try {
      const manifestPath = path.join(backupPath, ManifestManager.MANIFEST_FILENAME);

      if (await fileOperations.exists(manifestPath)) {
        // Existing backup location - validate and use existing manifest
        console.log('[Manifest] Found existing manifest, validating...');
        await this.validateExistingManifest(manifestPath);
      } else {
        // New backup location - create manifest with ALL current database state
        console.log('[Manifest] Creating initial manifest with current database state');
        await this.createInitialManifestFromDatabase(backupPath);
      }
    } catch (error) {
      console.error('Failed to initialize backup location:', error);
      throw new Error(`Cannot initialize backup location: ${error.message}`);
    }
  }

  /**
   * Handle backup location changes
   */
  async onBackupLocationChanged(newPath: string): Promise<void> {
    if (!newPath) return;

    await this.initializeBackupLocation(newPath);
    console.log('[Manifest] Backup location initialized:', newPath);
  }

  /**
   * Save manifest atomically
   */
  async saveManifest(directory: string, manifest: SyncManifest): Promise<void> {
    const manifestPath = path.join(directory, ManifestManager.MANIFEST_FILENAME);
    await fileOperations.writeFileAtomic(
      manifestPath, 
      JSON.stringify(manifest, null, 2)
    );
  }

  /**
   * Create a new default manifest
   */
  async createDefaultManifest(): Promise<SyncManifest> {
    return {
      version: ManifestManager.MANIFEST_VERSION,
      deviceId: await this.getDeviceId(),
      lastSync: new Date().toISOString(),
      items: [],
      deletions: []
    };
  }

  /**
   * Validate existing manifest file
   */
  private async validateExistingManifest(manifestPath: string): Promise<void> {
    try {
      const content = await fileOperations.readFileAtomic(manifestPath);
      const manifest = JSON.parse(content) as SyncManifest;

      // Basic validation
      if (!manifest.version || !manifest.deviceId || !Array.isArray(manifest.items)) {
        throw new Error('Invalid manifest structure');
      }

      console.log(`[Manifest] Validated existing manifest with ${manifest.items.length} items`);
    } catch (error) {
      console.error('Manifest validation failed:', error);
      throw new Error(`Invalid manifest file: ${error.message}`);
    }
  }

  /**
   * Create initial manifest from current database state
   */
  private async createInitialManifestFromDatabase(backupPath: string): Promise<void> {
    try {
      // Generate manifest with ALL current database items
      const manifest = await this.generateManifestFromDatabase();

      // Save manifest to backup location
      await this.saveManifest(backupPath, manifest);

      console.log(`[Manifest] Created initial manifest with ${manifest.items.length} items from database`);

      // Perform initial sync to create physical files
      await this.performInitialSync(backupPath);
    } catch (error) {
      console.error('Failed to create initial manifest from database:', error);
      throw error;
    }
  }

  /**
   * Perform initial sync to create physical files for existing database items
   */
  private async performInitialSync(backupPath: string): Promise<void> {
    try {
      console.log('[Manifest] Performing initial sync to create physical files...');

      // Import the unified sync engine to perform the sync
      const { unifiedSyncEngine } = await import('./unified-sync-engine');

      // Perform sync to create all physical files
      await unifiedSyncEngine.sync(backupPath);

      console.log('[Manifest] Initial sync completed successfully');
    } catch (error) {
      console.error('Initial sync failed:', error);
      // Don't throw - manifest is already created, sync can be retried later
      console.warn('[Manifest] Initial sync failed, but manifest was created. Files will be created on next sync.');
    }
  }

  /**
   * Build folder path by walking up parent_id chain
   * This creates the true database hierarchy without special book logic
   */
  private buildFolderPath(folderId: number, visitedIds: Set<number> = new Set()): string {
    const folder = this.folderMap.get(folderId);
    if (!folder) {
      console.warn(`[ManifestManager] Folder ${folderId} not found in folderMap`);
      return '';
    }

    // Check for circular references
    if (visitedIds.has(folderId)) {
      console.error(`Circular reference detected in folder hierarchy: folder ${folderId}`);
      return `_circular_ref_${folderId}/`;
    }

    // Add current folder to visited set
    visitedIds.add(folderId);

    if (folder.parent_id === null || folder.parent_id === undefined) {
      // Root folder
      const sanitizedName = this.sanitizeName(folder.name, `folder_${folder.id}`, '');
      console.log(`[ManifestManager] Root folder ${folderId} (${folder.name}) -> ${sanitizedName}/`);
      return sanitizedName + '/';
    }

    // Recursively build parent path with cycle detection
    const parentPath = this.buildFolderPath(folder.parent_id, visitedIds);
    const sanitizedName = this.sanitizeName(folder.name, `folder_${folder.id}`, parentPath);
    const fullPath = parentPath + sanitizedName + '/';
    console.log(`[ManifestManager] Folder ${folderId} (${folder.name}) with parent ${folder.parent_id} -> ${fullPath}`);
    return fullPath;
  }

  /**
   * Build folder hierarchy map from database folders
   * Must be called before path generation
   */
  private buildFolderHierarchy(folders: any[]): void {
    // Clear existing maps
    this.folderMap.clear();
    this.folderPaths.clear();

    // First pass: build folder lookup map
    folders.forEach(folder => {
      this.folderMap.set(folder.id, folder);
    });

    // Second pass: build paths for all folders
    folders.forEach(folder => {
      const path = this.buildFolderPath(folder.id);
      this.folderPaths.set(folder.id, path);
    });
  }

  /**
   * Generate manifest from current database state
   * Uses a read transaction to ensure consistent data across all queries
   */
  async generateManifestFromDatabase(): Promise<SyncManifest> {
    const manifest = await this.createDefaultManifest();
    
    try {
      // Execute all database queries within a single transaction for consistency
      const { books, folders, notes } = await withReadTransaction(async () => {
        // Get all books - include all fields for complete sync
        const booksQuery = `
          SELECT id, title as name, author, isbn, publication_date, description,
                 page_count, current_page, rating, status, olid, cover_url,
                 language, genres, custom_fields,
                 created_at, updated_at
          FROM books
          ORDER BY title
        `;
        const books = await dbAll<any>(booksQuery);
        
        // Get all folders with their relationships
        const foldersQuery = `
          SELECT f.id, f.name, f.book_id, f.parent_id, f.color, f.created_at, f.updated_at,
                 b.title as book_name
          FROM folders f
          LEFT JOIN books b ON f.book_id = b.id
          ORDER BY f.parent_id, f.name
        `;
        const folders = await dbAll<any>(foldersQuery);
        
        // Get all notes with their relationships
        const notesQuery = `
          SELECT n.id, n.title, n.content, n.html_content, n.type, n.color,
                 n.book_id, n.folder_id, n.last_viewed_at,
                 n.created_at, n.updated_at,
                 b.title as book_name,
                 f.name as folder_name
          FROM notes n
          LEFT JOIN books b ON n.book_id = b.id
          LEFT JOIN folders f ON n.folder_id = f.id
          ORDER BY n.book_id, n.folder_id, n.title
        `;
        const notes = await dbAll<any>(notesQuery);
        
        return { books, folders, notes };
      });
      
      // Clear collision map for fresh generation
      this.pathCollisionMap.clear();
      
      // Build folder hierarchy first - this creates the path mapping
      this.buildFolderHierarchy(folders);
      
      // Add books to manifest
      for (const book of books) {
        const bookId = `book_${book.id}`;
        const bookPath = `Books/${this.sanitizeName(book.name, bookId, 'Books/')}/`;
        const bookItem: ManifestItem = {
          id: bookId,
          type: 'book',
          name: book.name,
          path: bookPath,
          hash: this.calculateItemHash({
            id: book.id,
            name: book.name,
            type: 'book'
          }),
          modified: book.updated_at || book.created_at
        };
        
        // Add book metadata - use database field names for consistency
        const bookMetadata: any = {};
        if (book.author) bookMetadata.author = book.author;
        if (book.isbn) bookMetadata.isbn = book.isbn;
        if (book.publication_date) bookMetadata.publication_date = book.publication_date;
        if (book.description) bookMetadata.description = book.description;
        if (book.page_count !== undefined) bookMetadata.page_count = book.page_count;
        if (book.current_page !== undefined) bookMetadata.current_page = book.current_page;
        if (book.rating !== undefined) bookMetadata.rating = book.rating;
        if (book.status) bookMetadata.status = book.status;
        if (book.olid) bookMetadata.olid = book.olid;

        // OPTIMIZATION: Only store cover_url if it's NOT a base64 data URL to prevent manifest bloat
        // Base64 data URLs can be 50KB+ and should not be stored in manifest
        // Cover images are handled separately as .cover.jpg files in sync
        if (book.cover_url && !book.cover_url.startsWith('data:')) {
          bookMetadata.cover_url = book.cover_url;
        }

        if (book.language) bookMetadata.language = book.language;
        if (book.genres) bookMetadata.genres = book.genres;
        if (book.custom_fields) bookMetadata.custom_fields = book.custom_fields;
        if (book.created_at) bookMetadata.created_at = book.created_at;
        if (book.updated_at) bookMetadata.updated_at = book.updated_at;

        // Check if book has a cover in media_files table and add coverImage metadata
        try {
          const { dbGet } = await import('../../database/database-api');
          const mediaQuery = `SELECT * FROM media_files WHERE book_id = ? AND is_cover = 1`;
          const mediaFile = await dbGet<any>(mediaQuery, [book.id]);
          console.log(`[DEBUG] generateManifestFromDatabase - Book "${book.name}" (ID: ${book.id}) - Cover check:`, mediaFile ? 'FOUND' : 'NOT FOUND');
          if (mediaFile) {
            bookMetadata.coverImage = '.cover.jpg';
            console.log(`[DEBUG] generateManifestFromDatabase - Added coverImage to book "${book.name}"`);
          }
        } catch (error) {
          console.error(`Failed to check cover for book ${book.id}:`, error);
        }

        if (Object.keys(bookMetadata).length > 0) {
          bookItem.metadata = bookMetadata;
        }
        
        manifest.items.push(bookItem);
      }
      
      // Add folders to manifest
      for (const folder of folders) {
        const folderId = `folder_${folder.id}`;
        // Use pre-built folder path from hierarchy
        const folderPath = this.folderPaths.get(folder.id) || '';
        
        const relationships: any = {};
        
        // Store relationships for import resolution
        if (folder.book_id) {
          relationships.bookId = `book_${folder.book_id}`;
        }
        if (folder.parent_id) {
          relationships.parentId = `folder_${folder.parent_id}`;
        }
        
        const folderItem: ManifestItem = {
          id: folderId,
          type: 'folder',
          name: folder.name,
          path: folderPath,
          hash: this.calculateItemHash({
            id: folder.id,
            name: folder.name,
            type: 'folder',
            bookId: folder.book_id,
            parentId: folder.parent_id,
            color: folder.color
          }),
          modified: folder.updated_at || folder.created_at
        };
        
        if (Object.keys(relationships).length > 0) {
          folderItem.relationships = relationships;
        }

        // Add folder metadata (following the same pattern as notes)
        const folderMetadata: any = {};
        if (folder.color) folderMetadata.color = folder.color;
        if (folder.created_at) folderMetadata.created_at = folder.created_at;
        if (folder.updated_at) folderMetadata.updated_at = folder.updated_at;

        if (Object.keys(folderMetadata).length > 0) {
          folderItem.metadata = folderMetadata;
        }

        manifest.items.push(folderItem);
      }
      
      // Add notes to manifest
      for (const note of notes) {
        const noteId = `note_${note.id}`;
        let notePath: string;
        const relationships: any = {};
        
        // Store relationships for import resolution
        if (note.book_id) {
          relationships.bookId = `book_${note.book_id}`;
        }
        if (note.folder_id) {
          relationships.folderId = `folder_${note.folder_id}`;
        }
        
        // Determine note path based on folder_id
        if (note.folder_id) {
          // Note is in a folder - use the folder's path
          const folderPath = this.folderPaths.get(note.folder_id);
          if (folderPath) {
            const noteTitle = this.sanitizeName(note.title, noteId, folderPath);
            notePath = `${folderPath}${noteTitle}.noti`;
          } else {
            // Folder not found - create orphaned folder path to maintain structure
            console.warn(`Note ${note.id} references missing folder ${note.folder_id}. Creating orphaned folder path.`);
            const orphanedPath = `_orphaned_folders/folder_${note.folder_id}/`;
            const noteTitle = this.sanitizeName(note.title, noteId, orphanedPath);
            notePath = `${orphanedPath}${noteTitle}.noti`;
          }
        } else {
          // Standalone note at root
          const noteTitle = this.sanitizeName(note.title, noteId, '');
          notePath = `${noteTitle}.noti`;
        }
        
        const noteItem: ManifestItem = {
          id: noteId,
          type: 'note',
          name: note.title,
          path: notePath,
          hash: this.calculateItemHash({
            id: note.id,
            title: note.title,
            content: note.content,
            html_content: note.html_content,
            type: 'note'
          }),
          modified: note.updated_at || note.created_at
        };
        
        if (Object.keys(relationships).length > 0) {
          noteItem.relationships = relationships;
        }
        
        // Add note metadata (excluding html_content to keep manifest lightweight)
        const noteMetadata: any = {};
        if (note.type) noteMetadata.type = note.type; // The actual note type (text, markdown, etc.)
        if (note.color) noteMetadata.color = note.color;
        // Note: html_content intentionally excluded from manifest to prevent bloat
        // HTML content will be regenerated from markdown content during import
        if (note.last_viewed_at) noteMetadata.last_viewed_at = note.last_viewed_at;
        if (note.created_at) noteMetadata.created_at = note.created_at;
        if (note.updated_at) noteMetadata.updated_at = note.updated_at;
        
        if (Object.keys(noteMetadata).length > 0) {
          noteItem.metadata = noteMetadata;
        }
        
        manifest.items.push(noteItem);
      }
      
      return manifest;
    } catch (error) {
      console.error('Failed to generate manifest from database:', error);
      throw error;
    }
  }

  /**
   * Update manifest with exported item
   */
  updateManifestWithExport(manifest: SyncManifest, item: any, relativePath: string, metadata?: any): void {
    console.log(`[DEBUG] ManifestManager.updateManifestWithExport - Item: ${item.type}_${item.id}, Metadata passed:`, metadata ? 'YES' : 'NO');
    if (metadata && item.type === 'book') {
      console.log(`[DEBUG] Book metadata received:`, JSON.stringify(metadata, null, 2));
    }

    const manifestItem: ManifestItem = {
      id: `${item.type}_${item.id}`,
      type: item.type,
      name: item.name || item.title || 'Untitled',
      path: relativePath,
      hash: this.calculateItemHash(item),
      modified: item.updated_at || item.created_at || new Date().toISOString(),
      relationships: this.buildRelationships(item),
      metadata: metadata || this.extractMetadata(item)
    };

    if (item.type === 'book') {
      console.log(`[DEBUG] Final manifest item metadata for book:`, JSON.stringify(manifestItem.metadata, null, 2));
    }

    this.addItem(manifest, manifestItem);
  }

  /**
   * Extract metadata from item based on type
   */
  private extractMetadata(item: any): any {
    const metadata: any = {};
    
    if (item.type === 'note') {
      // Note: item.type is 'note' here, but the actual note type (text, markdown, etc.) is stored separately
      // For database notes, we would need a separate note_type field
      if (item.color) metadata.color = item.color;
      // Note: html_content intentionally excluded from manifest to prevent bloat
      if (item.last_viewed_at) metadata.last_viewed_at = item.last_viewed_at;
      if (item.created_at) metadata.created_at = item.created_at;
      if (item.updated_at) metadata.updated_at = item.updated_at;
      // Note: starred and is_favorite don't exist in the current schema
    } else if (item.type === 'book') {
      // Store essential book metadata (to replace .book-meta.json) - use database field names
      if (item.id !== undefined) metadata.id = item.id;
      if (item.title) metadata.title = item.title;
      if (item.author) metadata.author = item.author;
      if (item.isbn) metadata.isbn = item.isbn;
      if (item.publication_date) metadata.publication_date = item.publication_date;
      if (item.description) metadata.description = item.description;
      if (item.page_count !== undefined) metadata.page_count = item.page_count;
      if (item.current_page !== undefined) metadata.current_page = item.current_page;
      if (item.rating !== undefined) metadata.rating = item.rating;
      if (item.status) metadata.status = item.status;
      if (item.olid) metadata.olid = item.olid;

      // OPTIMIZATION: Only store cover_url if it's NOT a base64 data URL to prevent manifest bloat
      // Base64 data URLs can be 50KB+ and should not be stored in manifest
      if (item.cover_url && !item.cover_url.startsWith('data:')) {
        metadata.cover_url = item.cover_url;
      }

      if (item.language) metadata.language = item.language;
      if (item.genres) metadata.genres = item.genres;
      if (item.custom_fields) metadata.custom_fields = item.custom_fields;
      if (item.created_at) metadata.created_at = item.created_at;
      if (item.updated_at) metadata.updated_at = item.updated_at;
      // Note: cover image metadata will be set during export if cover exists
    } else if (item.type === 'folder') {
      if (item.color) metadata.color = item.color;
      if (item.created_at) metadata.created_at = item.created_at;
      if (item.updated_at) metadata.updated_at = item.updated_at;
    }
    
    return Object.keys(metadata).length > 0 ? metadata : undefined;
  }

  /**
   * Build relationship metadata for manifest item
   */
  private buildRelationships(item: any): any {
    const relationships: any = {};
    if (item.book_id) relationships.bookId = `book_${item.book_id}`;
    if (item.folder_id) relationships.folderId = `folder_${item.folder_id}`;
    if (item.parent_id) relationships.parentId = `folder_${item.parent_id}`;
    return Object.keys(relationships).length > 0 ? relationships : undefined;
  }

  /**
   * Add or update an item in the manifest
   */
  addItem(manifest: SyncManifest, item: ManifestItem): void {
    const existingIndex = manifest.items.findIndex(i => i.id === item.id);
    
    if (existingIndex >= 0) {
      // Update existing item
      manifest.items[existingIndex] = item;
    } else {
      // Add new item
      manifest.items.push(item);
    }
    
    // Remove from deletions if it was previously deleted
    manifest.deletions = manifest.deletions.filter(d => d.id !== item.id);
  }

  /**
   * Remove an item from manifest and add to deletions
   */
  removeItem(manifest: SyncManifest, itemId: string): void {
    const item = this.findItem(manifest, itemId);
    
    if (item) {
      // Remove from items
      manifest.items = manifest.items.filter(i => i.id !== itemId);
      
      // Add to deletions
      const deletion: DeletionRecord = {
        id: itemId,
        type: item.type,
        deletedAt: new Date().toISOString(),
        path: item.path
      };
      manifest.deletions.push(deletion);
    }
  }

  /**
   * Find an item in the manifest by ID
   */
  findItem(manifest: SyncManifest, itemId: string): ManifestItem | undefined {
    return manifest.items.find(item => item.id === itemId);
  }

  /**
   * Find item by composite ID (e.g., "book_123")
   */
  findItemById(manifest: SyncManifest, id: string): ManifestItem | null {
    return manifest.items.find(item => item.id === id) || null;
  }

  /**
   * Find item by local ID and type
   */
  findItemByLocalId(manifest: SyncManifest, localId: number, type: string): ManifestItem | null {
    const compositeId = `${type}_${localId}`;
    return this.findItemById(manifest, compositeId);
  }

  /**
   * Find item by path
   */
  findItemByPath(manifest: SyncManifest, path: string): ManifestItem | null {
    return manifest.items.find(item => item.path === path) || null;
  }

  /**
   * Get all items of a specific type
   */
  getItemsByType(manifest: SyncManifest, type: 'book' | 'folder' | 'note'): ManifestItem[] {
    return manifest.items.filter(item => item.type === type);
  }

  /**
   * Calculate hash of entire manifest for change detection
   */
  calculateManifestHash(manifest: SyncManifest): string {
    // Create a normalized representation for consistent hashing
    const normalized = {
      version: manifest.version,
      deviceId: manifest.deviceId,
      items: manifest.items
        .sort((a, b) => a.id.localeCompare(b.id))
        .map(item => ({
          id: item.id,
          type: item.type,
          name: item.name,
          path: item.path,
          hash: item.hash,
          modified: item.modified,
          relationships: item.relationships
        })),
      deletions: manifest.deletions
        .sort((a, b) => a.id.localeCompare(b.id))
        .map(del => ({
          id: del.id,
          type: del.type,
          deletedAt: del.deletedAt,
          path: del.path
        }))
    };
    
    return fileOperations.calculateHash(JSON.stringify(normalized));
  }

  /**
   * Merge two manifests for conflict resolution
   */
  async mergeManifests(local: SyncManifest, remote: SyncManifest): Promise<SyncManifest> {
    const merged: SyncManifest = {
      version: ManifestManager.MANIFEST_VERSION,
      deviceId: local.deviceId,
      lastSync: new Date().toISOString(),
      items: [],
      deletions: []
    };
    
    // Create maps for easier lookup
    const localItems = new Map(local.items.map(item => [item.id, item]));
    const remoteItems = new Map(remote.items.map(item => [item.id, item]));
    const localDeletions = new Map(local.deletions.map(del => [del.id, del]));
    const remoteDeletions = new Map(remote.deletions.map(del => [del.id, del]));
    
    // Process all unique item IDs
    const allItemIds = new Set([...Array.from(localItems.keys()), ...Array.from(remoteItems.keys())]);
    
    for (const itemId of Array.from(allItemIds)) {
      const localItem = localItems.get(itemId);
      const remoteItem = remoteItems.get(itemId);
      const localDeleted = localDeletions.has(itemId);
      const remoteDeleted = remoteDeletions.has(itemId);
      
      // Handle different scenarios
      if (localItem && remoteItem) {
        // Item exists in both - take the newer one
        if (new Date(localItem.modified) > new Date(remoteItem.modified)) {
          merged.items.push(localItem);
        } else {
          merged.items.push(remoteItem);
        }
      } else if (localItem && !remoteItem) {
        // Item only in local
        if (remoteDeleted) {
          // Remote deleted it - honor deletion
          const remoteDel = remoteDeletions.get(itemId)!;
          if (new Date(localItem.modified) > new Date(remoteDel.deletedAt)) {
            // Local was modified after deletion - keep it
            merged.items.push(localItem);
          } else {
            // Deletion is newer - add to deletions
            merged.deletions.push(remoteDel);
          }
        } else {
          // Simply not in remote - add it
          merged.items.push(localItem);
        }
      } else if (!localItem && remoteItem) {
        // Item only in remote
        if (localDeleted) {
          // Local deleted it - check timestamps
          const localDel = localDeletions.get(itemId)!;
          if (new Date(remoteItem.modified) > new Date(localDel.deletedAt)) {
            // Remote was modified after deletion - keep it
            merged.items.push(remoteItem);
          } else {
            // Deletion is newer - add to deletions
            merged.deletions.push(localDel);
          }
        } else {
          // Simply not in local - add it
          merged.items.push(remoteItem);
        }
      }
    }
    
    // Merge deletions (keep unique ones)
    const allDeletionIds = new Set([...Array.from(localDeletions.keys()), ...Array.from(remoteDeletions.keys())]);
    for (const delId of Array.from(allDeletionIds)) {
      // Skip if item is in merged items
      if (merged.items.some(item => item.id === delId)) {
        continue;
      }
      
      const localDel = localDeletions.get(delId);
      const remoteDel = remoteDeletions.get(delId);
      
      if (localDel && remoteDel) {
        // Take the newer deletion
        if (new Date(localDel.deletedAt) > new Date(remoteDel.deletedAt)) {
          merged.deletions.push(localDel);
        } else {
          merged.deletions.push(remoteDel);
        }
      } else if (localDel) {
        merged.deletions.push(localDel);
      } else if (remoteDel) {
        merged.deletions.push(remoteDel);
      }
    }
    
    return merged;
  }

  /**
   * Generate or retrieve persistent device ID
   */
  async getDeviceId(): Promise<string> {
    if (this.deviceId) {
      return this.deviceId;
    }
    
    try {
      // Try to get device ID from database
      const storedDeviceId = await getSetting('deviceId');
      
      if (storedDeviceId?.value) {
        this.deviceId = storedDeviceId.value;
        return this.deviceId;
      }
      
      // Generate new device ID and persist it
      this.deviceId = crypto.randomUUID();
      await setSetting('deviceId', this.deviceId, 'sync');
      
      return this.deviceId;
    } catch (error) {
      console.error('Failed to get/set device ID:', error);
      // If database fails, at least return a consistent ID for this session
      if (!this.deviceId) {
        this.deviceId = crypto.randomUUID();
      }
      return this.deviceId;
    }
  }

  /**
   * Calculate hash for an item
   */
  private calculateItemHash(data: any): string {
    return fileOperations.calculateHash(JSON.stringify(data));
  }

  /**
   * Sanitize name for file system usage with collision prevention
   */
  private sanitizeName(name: string, itemId: string, basePath: string = ''): string {
    // First, do basic sanitization
    let sanitized = name
      .replace(/[<>:"/\\|?*\x00-\x1F]/g, '_')
      .replace(/\s+/g, ' ')
      .trim();
    
    // Limit length to prevent filesystem issues (leave room for counter)
    if (sanitized.length > 200) {
      sanitized = sanitized.substring(0, 200).trim();
    }
    
    // Check for collisions
    const fullPath = basePath + sanitized;
    const pathKey = fullPath.toLowerCase(); // Handle case-insensitive filesystems
    
    if (!this.pathCollisionMap.has(pathKey)) {
      this.pathCollisionMap.set(pathKey, new Set());
    }
    
    const existingIds = this.pathCollisionMap.get(pathKey)!;
    
    // If this ID already has this path, it's fine
    if (existingIds.has(itemId)) {
      return sanitized;
    }
    
    // If another ID has this path, we need to add a counter
    if (existingIds.size > 0) {
      let counter = 2;
      let newName = `${sanitized}_${counter}`;
      let newPath = basePath + newName;
      let newPathKey = newPath.toLowerCase();
      
      // Find an available counter
      while (this.pathCollisionMap.has(newPathKey) && 
             this.pathCollisionMap.get(newPathKey)!.size > 0 &&
             !this.pathCollisionMap.get(newPathKey)!.has(itemId)) {
        counter++;
        newName = `${sanitized}_${counter}`;
        newPath = basePath + newName;
        newPathKey = newPath.toLowerCase();
      }
      
      sanitized = newName;
    }
    
    // Register this ID for this path
    existingIds.add(itemId);
    
    return sanitized;
  }
}

// Export singleton instance
export const manifestManager = new ManifestManager();