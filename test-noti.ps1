# PowerShell Script to Test .noti File Format
# Run with: .\test-noti.ps1

param(
    [switch]$Cleanup,
    [switch]$Help
)

if ($Help) {
    Write-Host "🧪 Noti Format Test Script" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Usage:"
    Write-Host "  .\test-noti.ps1           # Run tests"
    Write-Host "  .\test-noti.ps1 -Cleanup  # Clean up test files"
    Write-Host "  .\test-noti.ps1 -Help     # Show this help"
    Write-Host ""
    exit 0
}

# Check if we're in the right directory
if (!(Test-Path "package.json")) {
    Write-Host "❌ Error: Please run this script from the Noti project root directory" -ForegroundColor Red
    Write-Host "Expected to find package.json in current directory" -ForegroundColor Yellow
    exit 1
}

# Cleanup mode
if ($Cleanup) {
    Write-Host "🧹 Cleaning up test files..." -ForegroundColor Yellow
    
    if (Test-Path "test-output") {
        Remove-Item "test-output" -Recurse -Force
        Write-Host "✅ Removed test-output directory" -ForegroundColor Green
    }
    
    if (Test-Path "test-media") {
        Remove-Item "test-media" -Recurse -Force  
        Write-Host "✅ Removed test-media directory" -ForegroundColor Green
    }
    
    Write-Host "🏁 Cleanup complete!" -ForegroundColor Green
    exit 0
}

# Main test execution
Write-Host "🧪 Starting Noti File Format Tests..." -ForegroundColor Cyan
Write-Host "===========================================" -ForegroundColor Cyan
Write-Host ""

# Check if Node.js is available
try {
    $nodeVersion = node --version 2>$null
    Write-Host "✅ Node.js detected: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Error: Node.js not found. Please install Node.js first." -ForegroundColor Red
    exit 1
}

# Check if the test script exists
if (!(Test-Path "scripts/test-noti-simple.cjs")) {
    Write-Host "❌ Error: Test script not found at scripts/test-noti-simple.cjs" -ForegroundColor Red
    exit 1
}

Write-Host "🚀 Running .noti format tests..." -ForegroundColor Blue
Write-Host ""

# Run the test
try {
    & node scripts/test-noti-simple.cjs
    $testExitCode = $LASTEXITCODE
    
    Write-Host ""
    Write-Host "===========================================" -ForegroundColor Cyan
    
    if ($testExitCode -eq 0) {
        Write-Host "🎉 All tests completed successfully!" -ForegroundColor Green
        Write-Host ""
        Write-Host "Next steps:" -ForegroundColor Yellow
        Write-Host "1. Check the test file: test-output/test-note.noti" -ForegroundColor White
        Write-Host "2. Try importing it in the Noti app to verify it works" -ForegroundColor White
        Write-Host "3. Run '.\test-noti.ps1 -Cleanup' to clean up test files" -ForegroundColor White
    } else {
        Write-Host "❌ Tests failed with exit code: $testExitCode" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ Error running tests: $_" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "📋 Test Summary:" -ForegroundColor Cyan
if (Test-Path "test-output/test-note.noti") {
    $fileSize = (Get-Item "test-output/test-note.noti").Length
    Write-Host "  📁 Test file created: test-output/test-note.noti" -ForegroundColor White
    Write-Host "  📊 File size: $([math]::Round($fileSize/1KB, 2)) KB" -ForegroundColor White
    
    # Show a preview of the file structure
    try {
        $notiContent = Get-Content "test-output/test-note.noti" -Raw | ConvertFrom-Json
        Write-Host "  📄 Format version: $($notiContent.version)" -ForegroundColor White
        Write-Host "  📝 Note title: $($notiContent.metadata.title)" -ForegroundColor White
        Write-Host "  🎨 Note color: $($notiContent.metadata.color)" -ForegroundColor White
        Write-Host "  📊 Word count: $($notiContent.content.statistics.word_count)" -ForegroundColor White
    } catch {
        Write-Host "  ⚠️  Could not parse file preview" -ForegroundColor Yellow
    }
} else {
    Write-Host "  ❌ Test file was not created" -ForegroundColor Red
}

Write-Host ""
Write-Host "🔧 Troubleshooting:" -ForegroundColor Yellow
Write-Host "  - If tests fail, check the console output above for specific errors" -ForegroundColor White
Write-Host "  - Make sure you're running from the Noti project root directory" -ForegroundColor White
Write-Host "  - Run '.\test-noti.ps1 -Help' for usage information" -ForegroundColor White