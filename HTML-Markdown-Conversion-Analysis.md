# HTML to Markdown Conversion Analysis for Sync System

## Current System Overview

### What Actually Happens Now:
1. **During Editing**: TipTap editor maintains HTML and extracts plain text
   - `html_content`: Full rich HTML with all formatting
   - `content`: Plain text extracted via `editor.getText()` (NO formatting)
   
2. **During Export**: 
   - Saves only the plain text `content` field to .md files
   - All formatting (bold, italic, colors, fonts, etc.) is LOST
   
3. **During Import**:
   - Reads plain text from .md file
   - Sets `html_content` to null
   - Editor shows nothing because it only reads `html_content`

## Is Proper HTML ↔ Markdown Conversion Possible?

### Yes, but with significant limitations:

#### What CAN be preserved:
- **Basic formatting**: Bold, italic, strikethrough
- **Headings**: H1-H6
- **Lists**: Ordered, unordered, task lists
- **Links**: URLs and link text
- **Code**: Inline code and code blocks
- **Blockquotes**
- **Horizontal rules**
- **Basic tables** (with limitations)

#### What CANNOT be preserved in standard Markdown:
- **Colors**: Text colors, highlights
- **Fonts**: Font family, custom fonts
- **Advanced formatting**: Underline, superscript, subscript
- **Alignment**: Center, right alignment
- **Custom styles**: CSS classes, inline styles
- **Complex tables**: Merged cells, styling
- **Media positioning**: Image alignment, sizing
- **Interactive elements**: Checkboxes state in regular text

## Implementation Options

### Option 1: Enhanced Markdown with Frontmatter (Recommended)
```markdown
---
meta:
  defaultFont: "Montserrat"
  defaultColor: "#333333"
styles:
  - selector: "h1"
    color: "#0066cc"
  - selector: ".highlight"
    background: "#ffeb3b"
---

# My Note Title

This is **bold** and *italic* text with {.highlight}highlighted{/.highlight} content.

{color:#ff0000}Red text{/color} and {font:Arial}different font{/font}.
```

**Pros:**
- Preserves most styling information
- Remains readable as markdown
- Can be fully reconstructed to HTML

**Cons:**
- Non-standard markdown extensions
- More complex parsing required
- Larger file sizes

### Option 2: HTML-in-Markdown Hybrid
```markdown
# My Note Title

This is **bold** and *italic* text with <span style="background-color: #ffeb3b">highlighted</span> content.

<span style="color: #ff0000">Red text</span> and <span style="font-family: Arial">different font</span>.
```

**Pros:**
- Preserves ALL formatting
- Standard markdown parsers can handle it
- No information loss

**Cons:**
- Less readable as plain text
- Defeats purpose of markdown export
- Security concerns with arbitrary HTML

### Option 3: Dual Storage (Current System Enhanced)
Keep the current system but fix the import:
- Export: Continue saving plain text to .md
- Import: Convert markdown to basic HTML
- Accept formatting loss as trade-off for simplicity

## Performance Impact Analysis

### Conversion Overhead:
1. **HTML → Markdown**: ~5-10ms per note (with proper library)
2. **Markdown → HTML**: ~3-5ms per note (already exists)
3. **Enhanced Markdown Parsing**: ~10-20ms per note

### For 1000 notes:
- Basic conversion: ~8-15 seconds total
- Enhanced conversion: ~15-30 seconds total
- Current system (plain text): ~3-5 seconds total

## Recommended Solution

### Short Term (Immediate Fix):
Implement basic markdown-to-HTML conversion on import:

```typescript
// In unified-sync-engine.ts
import MarkdownIt from 'markdown-it';
const md = new MarkdownIt();

// During import:
html_content: content ? md.render(content) : '<p></p>'
```

### Long Term (Preserving Formatting):
Implement a custom HTML-to-Markdown converter using Turndown with custom rules:

```typescript
import TurndownService from 'turndown';

const turndownService = new TurndownService({
  headingStyle: 'atx',
  codeBlockStyle: 'fenced'
});

// Add custom rules for Noti-specific elements
turndownService.addRule('textColor', {
  filter: (node) => {
    return node.nodeName === 'SPAN' && node.style.color;
  },
  replacement: (content, node) => {
    return `{color:${node.style.color}}${content}{/color}`;
  }
});

// During export:
const markdownWithStyles = turndownService.turndown(note.html_content);
```

## Refactoring Required

### Minimal Refactoring (Basic Solution):
1. Add MarkdownIt to sync engine dependencies
2. Modify import logic (2 lines of code)
3. No UI changes needed

### Full Refactoring (Enhanced Solution):
1. Create custom HTML↔Markdown converter module
2. Update export logic to use enhanced markdown
3. Update import logic to parse enhanced markdown
4. Add tests for style preservation
5. Update backup format documentation
6. Consider backward compatibility

## Recommendation

**Implement the basic solution immediately** to fix the bug, then evaluate if users need formatting preservation. Most users likely care more about content than formatting in backups.

If formatting preservation is critical:
1. Survey users about formatting importance
2. Implement enhanced markdown as opt-in feature
3. Maintain backward compatibility
4. Document formatting limitations clearly

The current plain-text approach is actually quite elegant for backups - it ensures content is always readable and portable, even if formatting is lost.